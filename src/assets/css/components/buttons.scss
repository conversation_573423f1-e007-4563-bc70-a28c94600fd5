@import '../variables.scss';

button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.6rem 1.2rem;
    border-color: transparent;
    font-size: inherit;
    border-radius: .4rem;
    text-wrap: nowrap;
}

.primary-button {
    background-color: $primary-color ;
    border: 1px solid $primary-color;
    color: white;
    transition: all linear 0.2s;

    &:hover {
        background-color: white;
        border: 1px solid $primary-color;
        color: $primary-color;
    }
}

.secondary-button {
    background-color: transparent;
    border: 1px solid $primary-color-dark;
    color: $primary-color-dark;

    &:hover {
        background-color: $primary-color-dark;
        border: 1px solid white;
        color: white;
    }
}

@media  screen and (max-width: 540px) {

    .primary-button {
        padding: 0.6rem 0.2rem;
        width: 100%;
    }
    
}