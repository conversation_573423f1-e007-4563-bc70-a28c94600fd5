import { Flag01Icon, InformationCircleIcon } from 'hugeicons-react';
import React, { useState, useEffect, useRef } from 'react';
import { DataListInput, DateInput, TextInput } from '../forms/Input';
import { PrimaryButton, SecondaryButton } from '../forms/buttons';
import axios from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';
import { API_URL } from '../../api';


const formatDate = (dateObj) => {
    if (!dateObj) return null;

    try {
       
        if (dateObj.$y && dateObj.$M !== undefined && dateObj.$D) {
            const year = dateObj.$y;
            const month = (dateObj.$M + 1).toString().padStart(2, '0'); 
            const day = dateObj.$D.toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

  
        const date = new Date(dateObj);
        if (isNaN(date.getTime())) {
            return null;
        }

        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    } catch (error) {
        return null;
    }
};

const AddTargetPopup = ({ isOpen, onClose, onTargetAdded }) => {
    const [country, setCountry] = useState('');
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [targetValue, setTargetValue] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [measures, setMeasures] = useState([]);
    const popupRef = useRef(null);
    const [targetValues, setTargetValues] = useState('');
    const [dateError, setDateError] = useState('');
    const [isDateValid, setIsDateValid] = useState(false);
    const [hasYearData, setHasYearData] = useState(true);
    const valuesOptions = ['Lower is better', 'Greater is better', 'N/A'];
   

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose(); 
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

   

    const fetchMeasures = async (year) => {
        try {
            const response = await axios.get(`${API_URL}/measures/`, {
                params: { measure_year: year },
                headers: {
                    'Authorization': `Bearer ${Cookies.get('accessToken')}`,
                    'Content-Type': 'application/json',
                },
            });
            const measuresArray = response.data.map(item => item['name']);
            setMeasures(measuresArray);
            setHasYearData(measuresArray.length > 0);
        } catch (error) {
            toast.error('Failed to load measures');
        }
    };

    useEffect(() => {
        
        if (!startDate || !endDate) {
            setIsDateValid(false);
            return;
        }

        try {
           
            if (startDate.$y === undefined || endDate.$y === undefined) {
                setIsDateValid(false);
                return;
            }

            if (startDate.$y !== endDate.$y) {
                setIsDateValid(false);
                setDateError("Both Starting Date and Ending date should be in the same year.");
                return;
            }

            if (startDate.$m > endDate.$m || (startDate.$m === endDate.$m && startDate.$d > endDate.$d)) {
                setIsDateValid(false);
                setDateError("Ending date should be greater than Starting Date.");
                return;
            }

            const year = startDate.$y;
            fetchMeasures(year);
            setIsDateValid(true);
            setDateError('');
        } catch (error) {
            setIsDateValid(false);
        }
    }, [startDate, endDate]);



    const handleSubmit = async (e) => {
        e.preventDefault();
        if (isSubmitting) return;

        if (!country || !targetValue || !startDate || !endDate || !targetValues || !isDateValid) {
            toast.error('Please fill in all fields and ensure dates are valid.');
            return;
        }

        const newTarget = {
            measure: country,
            value: targetValue,
            value_condition: targetValues,
            starting_date: formatDate(startDate),
            end_date: formatDate(endDate),
            measure_year: startDate.$y,
        };


        
        setIsSubmitting(true);
        try {
            const token = Cookies.get('accessToken');
            const response = await axios.post(`${API_URL}/targets/new_target/`, newTarget, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                const createdTarget = response.data;
                onTargetAdded(createdTarget);
                onClose();

                toast.success("Target successfully added");
                window.location.href = '/targets/';
            }
            
        } catch (error) {
            if (error.response){
                toast.error('Error while adding target: ' + (error.response.data.error || 'Failed to add target. Please try again.'));
            } else {
                toast.error('Failed to add target. Please check your connection and try again.');
            }
            
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className='popup-overlay'>
            <form className='add-target-popup' onSubmit={handleSubmit} ref={popupRef}>
                <h1 className='title'>Add Target</h1>

                <div className='dateInput'>
                    <div className='label-dates'>
                        <label>Start Date</label>
                        <div
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                        >
                            <DateInput
                                date={startDate}
                                setDate={setStartDate}
                                placeholder="Start date"
                                label="Start date"
                                choices={['day', 'month', 'year']}
                            />
                        </div>
                    </div>
                    <div className='label-dates'>
                        <label>End Date</label>
                        <div
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                        >
                            <DateInput
                                date={endDate}
                                setDate={setEndDate}
                                placeholder="End date"
                                label="End date"
                                choices={['day', 'month', 'year']}
                            />
                        </div>
                    </div>
                </div>

                {startDate && endDate ? (
                    isDateValid ? (
                        hasYearData ? (
                            <>
                                <DataListInput
                                    iconClass={<Flag01Icon />}
                                    id="measures"
                                    name="measures"
                                    placeholder="Measure Name"
                                    value={country}
                                    setValue={setCountry}
                                    options={measures}
                                />

                                <div className='values'>
                                    <label htmlFor="value">This field only accepts numerical values.</label>
                                    <TextInput
                                        type='number'
                                        id='targetValue'
                                        name='targetValue'
                                        placeholder="Target value"
                                        value={targetValue}
                                        setValue={setTargetValue}
                                    />
                                </div>

                                <div className="field-with-info">
                                    <div className="field-label-with-info">
                                        <label>Optimization Criteria (How is improvement measured?)</label>
                                        {/* <div className="info-tooltip">
                                            <InformationCircleIcon size={16} color="#666" />
                                            <div className="tooltip-content">
                                                <strong>How is improvement measured?</strong>
                                                <ul>
                                                    <li><strong>Lower is better:</strong> Improvement means reducing the value (e.g., infection rates, readmission rates)</li>
                                                    <li><strong>Greater is better:</strong> Improvement means increasing the value (e.g., patient satisfaction scores, vaccination rates)</li>
                                                    <li><strong>N/A:</strong> No specific direction for improvement</li>
                                                </ul>
                                            </div>
                                        </div> */}
                                    </div>
                                    <DataListInput
                                        name="values"
                                        value={targetValues}
                                        setValue={setTargetValues}
                                        options={valuesOptions}
                                        placeholder="Select optimization criteria"
                                    />
                                </div>
                            </>
                        ) : (
                            <p style={{ color: 'red' }}>No measures available for the selected year.</p>
                        )
                    ) : (
                        <p className="date-error" style={{ color: 'red' }}>{dateError}</p>
                    )
                ) : (
                    <p>Please select reporting period (start and end dates)</p>
                )}

                <div className='buttons'>
                        <SecondaryButton
                        onClick={onClose}
                        buttonText='Cancel'
                    />
                <PrimaryButton
                        isLoading={isSubmitting}
                        type='submit'
                        processingText='Submitting'
                        buttonText='Save'
                    />
                
                   
                </div>
            </form>
        </div>
    );
};

export default AddTargetPopup;
