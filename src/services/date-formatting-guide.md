# Date Formatting Guide

## Overview

This guide explains how date formatting works in the application to ensure consistent date handling across different regions and locales.

## The Problem

Different regions use different date formats:
- US: MM/DD/YY (e.g., 03/01/25 means March 1, 2025)
- Most other countries: DD/MM/YY (e.g., 03/01/25 means January 3, 2025)

This can cause confusion when users from different regions interact with the application, especially when entering and viewing dates.

## Our Solution

We've implemented a standardized date formatting system that:

1. Uses ISO format (YYYY-MM-DD) for all API communications and data storage
2. Provides consistent display formatting for dates shown to users
3. Handles dayjs objects, Date objects, and string dates uniformly

## The formatDate Function

The core of our date handling is the `formatDate` function in `src/services/formatDate.js`:

```javascript
export const formatDate = (dateInput, format = 'api') => {
    if (!dateInput) return "";

    try {
        // Handle dayjs object
        if (dateInput.$y && dateInput.$M !== undefined && dateInput.$D) {
            const year = dateInput.$y;
            const month = (dateInput.$M + 1).toString().padStart(2, '0');
            const day = dateInput.$D.toString().padStart(2, '0');

            if (format === 'display') {
                return `${month}/${day}/${year.toString().slice(2)}`;
            } else if (format === 'iso') {
                return `${year}-${month}-${day}`;
            } else {
                return `${year}-${month}-${day}`; // API format
            }
        }

        // Handle Date object or string
        const date = new Date(dateInput);
        if (isNaN(date.getTime())) {
            console.error('Invalid date:', dateInput);
            return "";
        }

        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');

        if (format === 'display') {
            return `${month}/${day}/${year.toString().slice(2)}`;
        } else if (format === 'iso') {
            return `${year}-${month}-${day}`;
        } else {
            return `${year}-${month}-${day}`; // API format
        }
    } catch (error) {
        console.error('Error formatting date:', error);
        return "";
    }
};
```

## Format Options

The function supports three format options:

1. `'api'` (default): YYYY-MM-DD format for API communications
2. `'display'`: MM-DD-YYYY format for user display (as requested by the client)
3. `'iso'`: YYYY-MM-DD format for ISO standard compliance

## Using the FormattedDate Component

For displaying dates in components, use the `FormattedDate` component:

```jsx
import FormattedDate from '../services/formatDate';

// In your component:
<FormattedDate dateString={measure.starting_date} format="display" />
```

The `format` prop can be:
- `"display"` (default): Shows date in MM-DD-YYYY format
- `"api"`: Shows date in YYYY-MM-DD format
- `"iso"`: Shows date in YYYY-MM-DD format

## Date Input Handling

When working with date inputs:

1. Use the `DateInput` component for user date selection
2. When saving dates to the API, use `formatDate(dateObj, 'api')`
3. When displaying dates to users, use `formatDate(dateString, 'display')` or the `FormattedDate` component

## Example: Adding New Measure Data

```jsx
// When saving form data
const handleNewMeasureForm = async (e) => {
    e.preventDefault();

    const startingDateFormatted = formatDate(startingDate, 'api');
    const endingDateFormatted = formatDate(endDate, 'api');

    const newMeasureData = {
        measure: name,
        hospital: selectedHospital,
        value,
        starting_date: startingDateFormatted,
        end_date: endingDateFormatted
    };

    // Send to API...
};
```

## Example: Displaying Dates

```jsx
// In a table row
<td>
    <FormattedDate dateString={measure.starting_date} format="display" /> -
    <FormattedDate dateString={measure.end_date} format="display" />
</td>
```

## Troubleshooting

If you encounter date formatting issues:

1. Check that you're using the correct format parameter ('display', 'api', or 'iso')
2. Verify that the date input is valid (not null, undefined, or invalid format)
3. Use console.log to debug the date values at different stages
4. Remember that dayjs months are 0-indexed (January is 0, December is 11)

## Best Practices

1. Always use the formatDate function or FormattedDate component for date handling
2. Use 'api' format when sending dates to the backend
3. Use 'display' format when showing dates to users
4. Avoid direct string manipulation of dates
5. Don't rely on browser-specific or locale-specific date formatting
