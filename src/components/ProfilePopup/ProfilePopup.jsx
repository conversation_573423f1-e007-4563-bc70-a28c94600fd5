
import React from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import "../../assets/css/profile/profile.css";

const ProfilePopup = ({ 
  isPopupOpen, 
  closePopup, 
  step, 
  profile, 
  handleInputChange, 
  handleDateChange, 
  handleFileChange, 
  fileInputRef, 
  handleNextStep, 
  goToPreviousStep, 
  handleSubmit, 
  isSubmitting 
}) => {
  if (!isPopupOpen) return null;

  return (
    <div className="popup-overlayer">
      <div className="popup-content">
        {step === 1 && (
          <div className="form-container">
            <h2>Edit Profile</h2>
            <p>Lorem ipsum dolor sit amet consectetur. Vulputate lorem cras magna proin diam mauris. Erat commodo cras mattis eget nibh quam id.</p>
            <div className="fields-row">
              <div className="">
                <button onClick={() => fileInputRef.current.click()} className="change-picture-button">
                  Change Picture
                </button>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  ref={fileInputRef}
                  style={{ display: 'none' }}
                />
              </div>
              <div className="selectposition-id">
                <label>
                  <input
                    type="text"
                    name="ID"
                    value={profile.ID}
                    onChange={handleInputChange}
                    placeholder='ID'
                  />
                </label>
                <label>
                  <input
                    type="text"
                    name="position"
                    value={profile.position}
                    onChange={handleInputChange}
                    placeholder='Position'
                  />
                </label>
              </div>
            </div>
            <div className="cancel-submit">
              <button className='save' onClick={closePopup}>Cancel</button>
              <button className='cancel' onClick={handleNextStep}>Next</button>
            </div>
          </div>
        )}
        {step === 2 && (
          <div className="popup-content2">
            <h2>Edit Profile</h2>
            <p>Lorem ipsum dolor sit amet consectetur. Vulputate lorem cras magna proin diam mauris. Erat commodo cras mattis eget nibh quam id.</p>
            <div className="single-field">
              <label>
                <input
                  type="text"
                  name="username"
                  value={profile.username}
                  onChange={handleInputChange}
                  placeholder='Username'
                />
              </label>
            </div>
            <div className="hospital-lastname">
              <label>
                <input
                  type="text"
                  name="firstname"
value={profile.first_name}
                  onChange={handleInputChange}
                  placeholder='Firstname'
                />
              </label>
              <label>
                <input
                  type="text"
                  name="lastname"
value={profile.last_name}
                  onChange={handleInputChange}
                  placeholder='Lastname'
                />
              </label>
            </div>
            <div className="hospital-lastname">
              <label>
                <input
                  type="email"
                  name="email"
                  value={profile.email}
                  onChange={handleInputChange}
                  placeholder='Email Address'
                />
              </label>
              <label>
                <input
                  type="text"
                  name="hospital"
                  value={profile.hospital}
                  onChange={handleInputChange}
                  placeholder='Hospital'
                />
              </label>
            </div>
            <div className="single-field">
              <label>
                <input
                  type="text"
                  name="group"
                  value={profile.group}
                  onChange={handleInputChange}
                  placeholder="Group" 
                />
              </label>
            </div>
            <div className="date-field">
              <input
                type="date"
                name="dateAdded"
                value={profile.dateAdded}
                onChange={(e) => handleDateChange(e.target.value)}
              />
            </div>
            <div className="cancel-submit">
              <button className='cancel' onClick={goToPreviousStep}>Back</button>
              <button className='submit' onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfilePopup;
