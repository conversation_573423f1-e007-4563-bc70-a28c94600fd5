import React from 'react';
import StandardSearchBar from './StandardSearchBar';
import StandardFilters from './StandardFilters';
import './SearchAndFilters.css';

const SearchAndFilters = ({ 
    // Search props
    searchValue,
    onSearchChange,
    onSearch,
    searchPlaceholder = "Search...",
    isSearching = false,
    
    // Filter props
    children,
    onResetFilters,
    showResetFilters = false,
    filtersTitle,
    
    // Layout props
    layout = "stacked", // "stacked" | "inline" | "side-by-side"
    className = ""
}) => {
    const layoutClass = `search-filters-${layout}`;
    
    return (
        <div className={`search-and-filters ${layoutClass} ${className}`}>
            <div className="search-section">
                <StandardSearchBar
                    placeholder={searchPlaceholder}
                    value={searchValue}
                    onChange={onSearchChange}
                    onSearch={onSearch}
                    isLoading={isSearching}
                />
            </div>
            
            {children && (
                <div className="filters-section">
                    <StandardFilters
                        onReset={onResetFilters}
                        showReset={showResetFilters}
                        title={filtersTitle}
                        className={layout === "inline" ? "no-border" : ""}
                    >
                        {children}
                    </StandardFilters>
                </div>
            )}
        </div>
    );
};

export default SearchAndFilters;
