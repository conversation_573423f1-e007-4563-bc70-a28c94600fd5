@import "../variables";

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.total-hospital-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 20px;

  .icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  #acute {
    .icon-container {
      background-color: #f7fdff;
      padding: 10px;
      border-radius: 50%;
    }
  }

  #swing-bed {
    .icon-container {
      background-color: #fffdf7;
      padding: 10px;
      border-radius: 50%;
    }
  }

  #observation {
    .icon-container {
      background-color: #fff3f2;
      padding: 10px;
      border-radius: 50%;
    }
  }

  #emergency {
    .icon-container {
      background-color: #f4fbff;
      padding: 10px;
      border-radius: 50%;
    }
  }

  .total-card {
    display: flex;
    gap: 20px;
    align-items: center;
    border: 1px solid #e5e5e5;
    padding: 16px;
    border-radius: 8px;

    .col {
      display: flex;
      flex-direction: column;
      gap: 10px;

      p {
        color: #999999;
        font-size: 14px;
      }
    }
  }
}

.dashboard-container,
.hospitals-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  background-color: white;
  padding: 24px;
  border-radius: 0.5rem;

  .top-actions {
    display: flex;
    width: 100%;
    justify-content: flex-end;
    align-items: center;
    gap: 20px;

    .filter-container{
      display: flex;
      gap:12px;
    }

  
    .hospital-year{
      display: flex;
      gap: 10px;
    }
    .export-bttn {
      background-color: aliceblue;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem 2rem;
      height: fit-content;
      border-color: transparent;
      font-size: inherit;
      border-radius: 0.4rem;
      text-wrap: nowrap;
      cursor: pointer;
    }

    .export-icon {
      display: flex;
      gap: 5px;
      align-items: center;
      cursor: pointer;

     
    }

    select {
      padding: 25px 10px;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
    }

    .yearly-dates {
      display: flex;
      gap: 20px;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      padding: 10px;

      .yearly-col {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }
    }
  }

  .chart-row {
    width: 100%;
    display: grid;
    grid-template-columns: 65% 35%;
    gap: 20px;

    .chart-container {
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      padding: 20px;
      // width: 70%;

      .row {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .chart {
        width: 100%;
      }
    }

    .progress-container {
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 20px;
      // width: 30%!important;

      >.row {
        display: flex;
        flex-direction: column;
        gap: 20px;
        justify-content: space-between;
        // align-items: center;

        h4 {
          font-size: 18px;
        }
        .description{
          font-size: 16px;
          color: black;
          border-radius: 10px;
          font-style: italic;
          font-weight: bold;

        }
      }

      .hospital-progress {
        display: flex;
        flex-direction: column;
        gap: 20px;

        >.row {
          display: flex;
          gap: 10px;
          justify-content: space-between;
        

          .name {
            max-width: 50px;
          }
        }
      }
    }
  }

  .table-container {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .row {
      display: flex;
      gap: 20px;
      align-items: center;
    }

    table {
      border-collapse: collapse;
      width: 100%;

      tr {

        td,
        th {
          border: 1px solid #ccc;
          padding: 10px 20px;
          text-align: start;
        }

        .bm {
          background-color: #44536a;
          color: #ffffff;
        }
      }
    }
  }
}

.green-bg {
  background-color: #c6efcd;
  color: #016100;
}

.red-bg {
  background-color: #ffc7cd;
  color: #9c0007;
}

.yellow-bg {
  background-color: #ffeb9c;
  color: #9c5700;
}



@media screen and (max-width: 1080px) {
  .total-hospital-cards {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 20px;
  }

  .dashboard-page {
    .sidebar {
      display: none;
    }
  }
}
@media screen and (max-width:1040px) {
  .dashboard-container .chart-row{
    grid-template-columns: 100%;
  }
}


@media screen and (max-width: 860px) {
  .dashboard-container {
    .chart-row {
      flex-direction: column;

      .chart-container {
        width: 100%;
      }
    }
   

    .table-container {
      overflow-x: auto;
    }
  }
}
@media screen and (max-width:720px) {
  .dashboard-container{
    .top-actions {
      gap: 12px;
    }
  }
}

@media screen and (max-width: 600px) {
  .dashboard-container {

    .top-actions {
    flex-direction: column;
    .filter-container{
      display: flex;
      gap: 12px;
    }

 .export-bttn {
  padding: 0.8rem 0.5rem;

 }
    }

    .chart-row {
      .chart-container {
        padding-inline: 0px;

        .row {
          padding-inline: 20px;
        }
      }
    }
  }
  .hospitals-container {
    .top-actions{
      flex-direction: column;
        }
  }
  .single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .pie-chart{
    display: none;
  }
  

  .total-hospital-cards {
    grid-template-columns: repeat(1, 1fr);
  }
}

.hover-container{
  position: relative;
  display: flex;
  justify-content: space-between;
  .hover-description{
    display: none;
    background-color: #07aeef;
    padding: 10px 20px;
    border-radius: 5px;
    width: fit-content;
    color: white;
    z-index: 10;
    right: 0;
  }

  .hover-info{
    &:hover{
      .hover-description{
        position: absolute;
        display: block;
      }
    }
  }
  
 
}
