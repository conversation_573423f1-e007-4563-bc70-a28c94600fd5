.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.add-position-popup {
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 100%;
  position: relative;
  animation: slide-down 0.3s ease-out;
}
.add-position-popup #positionDescription {
  padding: 30px 10px;
  border-radius: 10px;
  outline: none;
  border: 1px solid #dbd4d4;
}/*# sourceMappingURL=add.css.map */