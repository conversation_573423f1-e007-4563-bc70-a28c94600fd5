import React from 'react';
import { CleanIcon } from 'hugeicons-react';
import { SecondaryButton } from '../forms/buttons';
import './StandardFilters.css';

const StandardFilters = ({ 
    children, 
    onReset, 
    showReset = false,
    className = "",
    title = "Filters"
}) => {
    return (
        <div className={`standard-filters ${className}`}>
            {title && <h4 className="filters-title">{title}</h4>}
            <div className="filters-content">
                {children}
            </div>
            {showReset && (
                <div className="filters-actions">
                    <SecondaryButton
                        iconClass={<CleanIcon />}
                        buttonText="Reset Filters"
                        onClick={onReset}
                    />
                </div>
            )}
        </div>
    );
};

// Individual filter components
export const FilterSelect = ({ 
    label, 
    value, 
    onChange, 
    options = [], 
    placeholder = "Select...",
    disabled = false 
}) => {
    return (
        <div className="filter-field">
            {label && <label className="filter-label">{label}</label>}
            <select
                value={value}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled}
                className="filter-select"
            >
                <option value="">{placeholder}</option>
                {options.map((option, index) => (
                    <option key={index} value={option.value || option}>
                        {option.label || option}
                    </option>
                ))}
            </select>
        </div>
    );
};

export const FilterInput = ({ 
    label, 
    value, 
    onChange, 
    placeholder = "",
    type = "text",
    disabled = false 
}) => {
    return (
        <div className="filter-field">
            {label && <label className="filter-label">{label}</label>}
            <input
                type={type}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                placeholder={placeholder}
                disabled={disabled}
                className="filter-input"
            />
        </div>
    );
};

export const FilterDateRange = ({ 
    label, 
    startDate, 
    endDate, 
    onStartDateChange, 
    onEndDateChange,
    disabled = false 
}) => {
    return (
        <div className="filter-field">
            {label && <label className="filter-label">{label}</label>}
            <div className="date-range-inputs">
                <input
                    type="date"
                    value={startDate}
                    onChange={(e) => onStartDateChange(e.target.value)}
                    disabled={disabled}
                    className="filter-input date-input"
                    placeholder="Start Date"
                />
                <span className="date-separator">to</span>
                <input
                    type="date"
                    value={endDate}
                    onChange={(e) => onEndDateChange(e.target.value)}
                    disabled={disabled}
                    className="filter-input date-input"
                    placeholder="End Date"
                />
            </div>
        </div>
    );
};

export default StandardFilters;
