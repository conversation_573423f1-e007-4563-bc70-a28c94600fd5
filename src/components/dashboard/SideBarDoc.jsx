

// import React from 'react'
// import { Link } from 'react-router-dom'

// const SideBarDoc = () => {
//   return (
//     <div className='sidebar-doc'>
//     <div className='contents'>
//     <Link to='/login'>Login</Link>
//     <Link to='/dashboard'>Dashboard</Link>
//     </div>
 

//     </div>
//   )
// }

// export default SideBarDoc


import React, { useState, useEffect } from "react";


const SideBarDoc = () => {
  const Dropdown = ({ title, children }) => (
    <div className="dropdown">
      <h2 className="droplogin">{title}</h2>
      <div className="login-content">{children}</div>
    </div>
  );
  const [isSidebarVisible, setIsSidebarVisible] = useState(
    window.innerWidth > 800
  );

  // Smooth scroll to target section
  const handleScrollToSection = (id) => {
    const targetElement = document.getElementById(id);
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: "smooth" });
      if (window.innerWidth <= 800) {
        setIsSidebarVisible(false); // Hide sidebar on smaller screens after clicking
      }
    }
  };

  // Show sidebar
  const handleShowSidebar = () => {
    setIsSidebarVisible(true);
  };

  // Hide sidebar
  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  // Update visibility on window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 800) {
        setIsSidebarVisible(true);
      } else {
        setIsSidebarVisible(false);
      }
    };
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <>
     <div className='sidebar-doc'>

     
      {/* <button
        id="showSidebar"
        onClick={handleShowSidebar}
        style={{ display: isSidebarVisible ? "none" : "block" }}
      >
        Show Sidebar
      </button>

      {isSidebarVisible && (
        <div className="sidebar" id="sidebar">
          <i
            className="fa-solid fa-xmark close"
            id="closeSidebar"
            onClick={handleCloseSidebar}
          ></i> */}

          {/* Logging In Section */}

          <div className='contents'>
          <Dropdown title="Logging In">
            <a onClick={() => handleScrollToSection("login")}>Login</a>
            {/* <a onClick={() => handleScrollToSection("verify")}>Verify Account</a>
            <a onClick={() => handleScrollToSection("forgetPassword")}>
              Forget Password
            </a> */}
          </Dropdown>

          {/* Dashboard Section */}
          <Dropdown title="Dashboard">
            <a onClick={() => handleScrollToSection("dashboard")}>Dashboard</a>
            <a onClick={() => handleScrollToSection("hospitals")}>Hospitals</a>
            <a onClick={() => handleScrollToSection("singleHospitalDetail")}>
              Single Hospital
            </a>
          </Dropdown>

          {/* User Section */}
          <Dropdown title="User">
            <a onClick={() => handleScrollToSection("userList")}>User List</a>
            <a onClick={() => handleScrollToSection("newUser")}>Add New User</a>
            <a onClick={() => handleScrollToSection("editUser")}>Edit User</a>
            <a onClick={() => handleScrollToSection("deleteUser")}>
              Delete User
            </a>
            <p>User Details</p>
            <a onClick={() => handleScrollToSection("editDetails")}>
              Edit Details
            </a>
            <a onClick={() => handleScrollToSection("changePassword")}>
              Change Password
            </a>
          </Dropdown>

          {/* Other Pages Section */}
          <Dropdown title="Other Pages">
            <a onClick={() => handleScrollToSection("measuresList")}>
              Measures
            </a>
            <a onClick={() => handleScrollToSection("addMeasure")}>
              Add Measure
            </a>
            <a onClick={() => handleScrollToSection("editMeasure")}>
              Edit Measure
            </a>
            <a onClick={() => handleScrollToSection("deleteMeasure")}>
              Delete Measure
            </a>
            <a onClick={() => handleScrollToSection("positions")}>Positions</a>
            <a onClick={() => handleScrollToSection("addPositions")}>
              Add Positions
            </a>
            <a onClick={() => handleScrollToSection("editPositions")}>
              Edit Position
            </a>
            <a onClick={() => handleScrollToSection("deletePosition")}>
              Delete Position
            </a>
            <a onClick={() => handleScrollToSection("targets")}>Targets</a>
            <a onClick={() => handleScrollToSection("addTarget")}>Add Target</a>
            <a onClick={() => handleScrollToSection("editTarget")}>
              Edit Target
            </a>
            <a onClick={() => handleScrollToSection("deleteTarget")}>
              Delete Target
            </a>
            <a onClick={() => handleScrollToSection("notifications")}>
              Notifications
            </a>
            <a onClick={() => handleScrollToSection("reports")}>Reports</a>
          </Dropdown>
        </div>
      {/* )} */}
      {/* </div> */}
      </div>
    </>
  );
};

export default SideBarDoc;
