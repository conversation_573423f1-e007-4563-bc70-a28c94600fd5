import {Delete01Icon, PencilEdit02Icon } from 'hugeicons-react'
export const targetListData = [
    {
      id: "COH-T-001",
      measureName: "CAH 30 Day Re-admission Rate",
      target: 12,
      dateCreated: "2024-12-01",
      startingDate: null,
      endingTarget: null,
      isChecked: true, 
      actionIcons: {
deleteIcon: <Delete01Icon />, 
        editIcon: <PencilEdit02Icon />    
      },
    },
    {
      id: "COH-T-002",
      measureName: "Left Without Being Seen",
      target: 12,
      dateCreated: "2024-12-01",
      startingDate: null,
      endingTarget: null,
      isChecked: false,
      actionIcons: {
        deleteIcon: <Delete01Icon />, 
        editIcon: <PencilEdit02Icon />    
      },
    },
    {
      id: "COH-T-003",
      measureName: "ED Mortality Rate",
      target: 12,
      dateCreated: "2024-12-01",
      startingDate: null,
      endingTarget: null,
      isChecked: false,
      actionIcons: {
      deleteIcon: <Delete01Icon />, 
        editIcon: <PencilEdit02Icon />    
      },
    },
    {
      id: "COH-T-004",
      measureName: "Inpatient Mortality Rate",
      target: 12,
      dateCreated: "2024-12-01",
      startingDate: "2025-01-01",
      endingTarget: "2026-01-01",
      isChecked: true,
      actionIcons: {
       deleteIcon: <Delete01Icon />, 
        editIcon: <PencilEdit02Icon />    
      },
    },
    {
      id: "COH-T-005",
      measureName: "Pressure Ulcer",
      target: 12,
      dateCreated: "2024-12-01",
      startingDate: null,
      endingTarget: null,
      isChecked: false,
      actionIcons: {
        deleteIcon: <Delete01Icon />, 
        editIcon: <PencilEdit02Icon />    
      },
    },
    {
      id: "COH-T-006",
      measureName: "Total Complaints (IP & ED)",
      target: 12,
      dateCreated: "2024-12-01",
      startingDate: null,
      endingTarget: null,
      isChecked: false,
      actionIcons: {
       deleteIcon: <Delete01Icon />, 
        editIcon: <PencilEdit02Icon />    
      },
    },
    {
      id: "COH-T-007",
      measureName: "Hospital Acquired Infections",
      target: 12,
      dateCreated: "2024-12-01",
      startingDate: null,
      endingTarget: null,
      isChecked: false,
      actionIcons: {
       deleteIcon: <Delete01Icon />, 
        editIcon: <PencilEdit02Icon />    
      },
    },
  ];
  