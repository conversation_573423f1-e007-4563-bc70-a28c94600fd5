# Loading Components Usage Guide

This guide explains how to use the loading components in your application.

## Available Components

1. **LoginLoading** - For login screen loading
2. **TableLoading** - For table-specific loading
3. **PageLoading** - For full page loading

## How to Use TableLoading

The TableLoading component is designed to only affect tables while allowing other UI elements to remain functional. Here's how to implement it:

### Step 1: Import the Component

```jsx
import TableLoading from '../../components/loading/TableLoading';
```

### Step 2: Add CSS to Your Table Container

Make sure your table container has position relative:

```css
.table-container {
  position: relative;
}
```

### Step 3: Add the Loading Component Inside the Table Container

```jsx
<div className="table-container">
  {isLoading && <TableLoading />}
  <table>
    {/* Your table content */}
  </table>
</div>
```

This approach ensures that:
- The loading indicator only appears over the table
- Other UI elements remain accessible
- Users can still use buttons, search, and other functionality while data is loading

## How to Use LoginLoading

The LoginLoading component provides a full-screen loading state during authentication:

```jsx
import LoginLoading from '../../components/loading/LoginLoading';

// In your component:
{isAuthenticating && <LoginLoading />}
```

## How to Use PageLoading

The PageLoading component is for general page loading states:

```jsx
import PageLoading from '../../components/loading/PageLoading';

// In your component:
{isPageLoading && <PageLoading />}
```

## Best Practices

1. Use TableLoading for data fetching operations that should not block the entire UI
2. Use LoginLoading only during authentication processes
3. Use PageLoading sparingly, only when the entire page needs to be blocked
4. Always position your loading components correctly to avoid unintended UI blocking

## Implementation Notes

The loading components have been updated to ensure they only affect their intended areas:

1. TableLoading now has `pointer-events: none` to allow clicks to pass through
2. Global loading overlays have been disabled with `display: none !important`
3. All table containers have `position: relative` to properly contain loading indicators
4. Charts and other data containers also have proper positioning for loading indicators

## Troubleshooting

If you encounter issues with loading indicators:

1. Check that your container has `position: relative`
2. Ensure the TableLoading component is a direct child of the container
3. Verify that you're using the correct loading component for your use case
4. Check for any CSS conflicts that might be affecting the loading overlay
