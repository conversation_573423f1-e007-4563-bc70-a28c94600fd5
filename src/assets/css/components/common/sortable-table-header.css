.sortable-header {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
    position: relative;
}

.sortable-header:hover {
    background-color: #f8fafc;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 8px 12px;
}

.header-label {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.sort-icons {
    display: flex;
    flex-direction: column;
    gap: 1px;
    margin-left: auto;
    flex-shrink: 0;
}

.sort-icon {
    transition: all 0.2s ease;
    display: block;
}

.sort-icon.inactive {
    color: #d1d5db;
    opacity: 0.5;
}

.sort-icon.active {
    color: #3b82f6;
    opacity: 1;
}

.sortable-header:hover .sort-icon.inactive {
    color: #9ca3af;
    opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
    .header-content {
        padding: 6px 8px;
        gap: 4px;
    }
    
    .header-label {
        font-size: 13px;
    }
    
    .sort-icons {
        gap: 0;
    }
}

/* Table-specific styling */
table .sortable-header {
    border-bottom: 2px solid #e5e7eb;
    background-color: #f9fafb;
}

table .sortable-header:hover {
    background-color: #f3f4f6;
}

/* Active sort styling */
.sortable-header.active-sort {
    background-color: #eff6ff;
}

.sortable-header.active-sort .header-label {
    color: #1d4ed8;
}

.sortable-header.active-sort:hover {
    background-color: #dbeafe;
}
