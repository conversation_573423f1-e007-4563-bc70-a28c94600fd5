import React, { useRef, useEffect } from 'react';
import { PrimaryButton, SecondaryButton } from '../../buttons';
import '../../../../assets/css/components/forms/forms.css';

/**
 * Confirmation dialog for when a measure ID is already in use
 * 
 * @param {Object} props Component props
 * @param {string} props.measureName The name of the measure
 * @param {Function} props.onConfirm Callback when user confirms
 * @param {Function} props.onCancel Callback when user cancels
 * @returns {JSX.Element} Confirmation dialog component
 */
const ConfirmMeasureDialog = ({ measureName, onConfirm, onCancel }) => {
    const dialogRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (dialogRef.current && !dialogRef.current.contains(e.target)) {
                onCancel();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onCancel]);

    return (
        <div className="overlay">
            <div className="popup-user confirm-dialog" ref={dialogRef}>
                <div className="confirm-content">
                    <h2>Measure Already In Use</h2>
                    <p>
                        The measure <strong>"{measureName}"</strong> is already in use from a previous or current reporting period.
                    </p>
                    <p>
                        Do you want to continue using this measure ID?
                    </p>
                    <div className="popup-buttons">
                        <SecondaryButton 
                            onClick={onCancel} 
                            buttonText="Cancel" 
                        />
                        <PrimaryButton 
                            onClick={onConfirm} 
                            buttonText="Continue" 
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ConfirmMeasureDialog;
