.step-measure-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.step-measure-form {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.step-header {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e5e7eb;
}

.step-header h2 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
}

.step-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  position: relative;
}

.step-indicator::before {
  content: '';
  position: absolute;
  top: 16px;
  left: 32px;
  right: 32px;
  height: 2px;
  background: #e5e7eb;
  z-index: 1;
}

.step {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 2;
  background: white;
  padding: 0 8px;
}

.step.active .step-number {
  background: #3b82f6;
  color: white;
}

.step.active .step-title {
  color: #1f2937;
  font-weight: 500;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 14px;
  flex-shrink: 0;
}

.step-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.step-title {
  font-size: 12px;
  font-weight: 400;
  color: #6b7280;
  white-space: nowrap;
}

.step-subtitle {
  font-size: 10px;
  color: #9ca3af;
  white-space: nowrap;
}

.step-content {
  padding: 24px;
  min-height: 300px;
}

.step-1, .step-2, .step-3 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.measure-instructions {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.measure-instructions h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.measure-instructions ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.measure-instructions li {
  font-size: 13px;
  color: #4b5563;
  margin-bottom: 4px;
}

.text-area-measure {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  box-sizing: border-box;
}

.text-area-measure:focus {
  outline: none;
  border-color: #3b82f6;
}

.value-type {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px 24px 24px;
  border-top: 1px solid #e5e7eb;
}

.left-actions, .right-actions {
  display: flex;
  gap: 12px;
}

.text-red {
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .step-measure-form {
    width: 95%;
    max-height: 95vh;
  }
  
  .step-indicator {
    flex-direction: column;
    gap: 12px;
  }
  
  .step-indicator::before {
    display: none;
  }
  
  .step {
    justify-content: flex-start;
    padding: 0;
  }
  
  .step-title {
    white-space: normal;
  }
  
  .step-subtitle {
    white-space: normal;
  }
  
  .step-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .left-actions, .right-actions {
    width: 100%;
    justify-content: center;
  }
  
  .value-type {
    gap: 12px;
  }
}
