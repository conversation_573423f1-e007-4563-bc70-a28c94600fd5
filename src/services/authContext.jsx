import { useEffect, useState } from "react";
import { jwtDecode } from "jwt-decode";
import CryptoJ<PERSON> from "crypto-js";
import { useNavigate } from "react-router-dom";
import Cookies from 'js-cookie';
import axios from "axios";
import { API_URL } from "../api";

export const useAuthentication = () => {
    const navigate = useNavigate();
    const encryptionKey = process.env.REACT_APP_ENCRYPTION_KEY;
    const domainName = process.env.REACT_APP_DOMAIN_NAME;
    const params = new URLSearchParams(window.location.search);
    const encryptedToken = params.get('token');

    const [isAuth, setIsAuth] = useState(false);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const checkAuthentication = async () => {
            try {
                setLoading(true);
                if (encryptedToken) {
                    if (!encryptionKey) {
                        window.location.href = `${domainName}/?error=Invalid encryption key`;
                        return;
                    }

                    const decryptedToken = decryptToken(encryptedToken, encryptionKey);
                    if (!decryptedToken) {
                        console.error("Failed to decrypt token");
                        setLoading(false);
                        return;
                    }

                    localStorage.setItem("access", decryptedToken);
                    Cookies.set('accessToken', decryptedToken);
                    await fetchUserPermissions(decryptedToken);
                }


                let token = Cookies.get('accessToken') || localStorage.getItem("access");
                console.log("Retrieved Token:", token);

                if (token) {
                    const tokenExpired = isTokenExpired(token);
                    if (tokenExpired) {
                        console.log("Token expired");
                        setIsAuth(false);
                        Cookies.remove('accessToken');
                        Cookies.remove('refreshToken');
                        localStorage.removeItem("access");
                        navigate('/login');
                    } else {
                        console.log("Token valid, setting auth to true");
                        setIsAuth(true);
                        Cookies.set('accessToken', token);
                        localStorage.setItem("access", token);
                        if (encryptedToken) {
                            navigate(window.location.pathname, { replace: true });
                        }
                    }
                } else {
                    console.log("No token found");
                    setIsAuth(false);
                    navigate('/login');
                }
            } catch (error) {
                console.error("Authentication error:", error);
                setIsAuth(false);
            } finally {
                setLoading(false);
            }
        };

        checkAuthentication();
    }, [encryptedToken, encryptionKey, domainName, navigate]);

    const logout = () => {
        localStorage.clear();
        Cookies.remove('accessToken');
        Cookies.remove('refreshToken');
        window.location.href = process.env.REACT_APP_DOMAIN_NAME;
    };

    return { isAuth, loading, logout };
};

function isTokenExpired(token) {
    try {
        if (!token) return true;
        const decodeToken = jwtDecode(token);
        const expirationTime = decodeToken.exp * 1000;
        const hasExpired = expirationTime < Date.now();
        console.log("Token expiration check:", {
            expirationTime: new Date(expirationTime),
            now: new Date(),
            hasExpired
        });
        return hasExpired;
    } catch (error) {
        console.error("Invalid token:", error);
        return true;
    }
}

function decryptToken(encryptedToken, key) {
    if (!encryptedToken) return null;
    try {
        const iv = CryptoJS.enc.Hex.parse(encryptedToken.slice(0, 32));
        const encryptedData = CryptoJS.enc.Hex.parse(encryptedToken.slice(32));

        const decrypted = CryptoJS.AES.decrypt(
            { ciphertext: encryptedData },
            CryptoJS.enc.Utf8.parse(key.padEnd(32, '0')),
            {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            }
        );

        const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
        return decryptedText || null;
    } catch (error) {
        console.error("Decryption failed:", error);
        return null;
    }
}


const fetchUserPermissions = async (accessToken) => {
    try {
        const response = await axios.get(`${API_URL}/user/profile/`, {
            headers: { Authorization: `Bearer ${accessToken}` },
        });
        const user = response.data;

        localStorage.setItem('position', user.position);
        localStorage.setItem('hospital', user.hospital);
        localStorage.setItem('hospital_id', user.hospital_id);

        return user;
    } catch (error) {
        console.error('Error fetching user permissions:', error);
        throw error;
    }
};