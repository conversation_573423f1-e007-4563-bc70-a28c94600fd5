import React, { useState } from 'react';
import { ArrowUp01Icon, ArrowDown01Icon, Search01Icon } from 'hugeicons-react';
import { Select, MenuItem, TextField } from '@mui/material';
import '../../assets/css/components/common/filterable-table-header.css';

const FilterableTableHeader = ({ 
    label, 
    sortKey, 
    currentSort, 
    onSort, 
    onFilter,
    filterValue = '',
    filterOptions = [],
    filterType = 'text', // 'text', 'select'
    className = '',
    ...props 
}) => {
    const [showFilter, setShowFilter] = useState(false);

    const handleSort = () => {
        if (currentSort.key === sortKey) {
            // If already sorting by this column, toggle direction
            const newDirection = currentSort.direction === 'asc' ? 'desc' : 'asc';
            onSort({ key: sortKey, direction: newDirection });
        } else {
            // If sorting by different column, start with ascending
            onSort({ key: sortKey, direction: 'asc' });
        }
    };

    const handleFilterChange = (value) => {
        onFilter(sortKey, value);
    };

    const getSortIcon = () => {
        if (currentSort.key !== sortKey) {
            return (
                <div className="sort-icons">
                    <ArrowUp01Icon size={12} className="sort-icon inactive" />
                    <ArrowDown01Icon size={12} className="sort-icon inactive" />
                </div>
            );
        }

        return (
            <div className="sort-icons">
                <ArrowUp01Icon 
                    size={12} 
                    className={`sort-icon ${currentSort.direction === 'asc' ? 'active' : 'inactive'}`} 
                />
                <ArrowDown01Icon 
                    size={12} 
                    className={`sort-icon ${currentSort.direction === 'desc' ? 'active' : 'inactive'}`} 
                />
            </div>
        );
    };

    const renderFilter = () => {
        if (!showFilter) return null;

        if (filterType === 'select') {
            return (
                <Select
                    value={filterValue}
                    onChange={(e) => handleFilterChange(e.target.value)}
                    displayEmpty
                    size="small"
                    className="filter-select"
                >
                    <MenuItem value="">All</MenuItem>
                    {filterOptions.map((option, index) => (
                        <MenuItem key={index} value={option.value || option}>
                            {option.label || option}
                        </MenuItem>
                    ))}
                </Select>
            );
        }

        return (
            <TextField
                value={filterValue}
                onChange={(e) => handleFilterChange(e.target.value)}
                placeholder={`Filter ${label.toLowerCase()}...`}
                size="small"
                className="filter-input"
                variant="outlined"
            />
        );
    };

    return (
        <th className={`filterable-header ${className}`} {...props}>
            <div className="header-content">
                <div className="header-top" onClick={handleSort}>
                    <span className="header-label">{label}</span>
                    <div className="header-icons">
                        {getSortIcon()}
                        <Search01Icon
                            size={12}
                            className={`filter-icon ${showFilter ? 'active' : ''}`}
                            onClick={(e) => {
                                e.stopPropagation();
                                setShowFilter(!showFilter);
                            }}
                        />
                    </div>
                </div>
                {renderFilter()}
            </div>
        </th>
    );
};

export default FilterableTableHeader;
