.position-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background-color: white;
  border-radius: 0.5rem;
}
.position-page .position {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
}
.position-page .primary-button {
  width: -moz-fit-content;
  width: fit-content;
}

.card-title {
  color: #91979b;
}

.position-data {
  position: absolute;
  width: 50%;
  top: 24%;
  left: 154px;
  background-color: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 10px;
  display: flex;
}
.position-data .action-button {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.position-data .action-button .edit-text {
  color: #07AEEF;
}
.position-data .action-button .line {
  width: 100%;
  height: 1px;
  background-color: #6c6565;
}
.position-data .action-button p {
  display: flex;
  align-items: center;
  margin: 0;
  gap: 8px;
  cursor: pointer;
}

.position-card {
  position: relative;
  border: 1px solid #E5E5E5;
  border-radius: 8px;
  padding: 32px 21px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #FFFFFF;
}
.position-card .position-date {
  display: flex;
  gap: 5px;
}
.position-card .position-contents {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.position-card .position-contents .id_icon {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
.position-card .position-contents .id_icon button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}
.position-card .position-contents .id_icon button:focus {
  outline: none;
}

.icon-text {
  display: flex;
  gap: 12px;
  align-items: center;
}
.icon-text .icon {
  font-size: 48px;
  color: red;
}
.icon-text .text {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.icon-text .text .delete-id {
  font-weight: bolder;
}

.view-position-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.view-position-popup-overlay .view-position-popup {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
.view-position-popup-overlay .view-position-popup .description {
  color: gray;
}
.view-position-popup-overlay .view-position-popup .details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.view-position-popup-overlay .view-position-popup .details .positionId {
  color: #0E76BC;
}
.view-position-popup-overlay .view-position-popup .permissions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.view-position-popup-overlay .view-position-popup .permissions ul {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 0 18px;
}
.view-position-popup-overlay .view-position-popup .permissions ul li {
  color: gray;
}
.view-position-popup-overlay .view-position-popup button {
  margin-top: 15px;
  padding: 8px 12px;
  border: none;
  background-color: #0E76BC;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}
.view-position-popup-overlay button:hover {
  background-color: #0056b3;
}

@media screen and (max-width: 920px) {
  .position-page .position {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media screen and (max-width: 600px) {
  .position-page .position {
    padding: 10px;
  }
}/*# sourceMappingURL=position.css.map */