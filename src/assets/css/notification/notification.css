.Notifications-page {
  display: flex;
  gap: 24px;
  flex-direction: column;
  background-color: white;
  padding: 24px;
  border-radius: 10px;
}
.Notifications-page .input-check {
  display: flex;
  gap: 10px;
}
.Notifications-page .pagination {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 10px 0;
}
.Notifications-page .Notifications {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}
.Notifications-page .read {
  color: green;
  font-weight: bold;
}
.Notifications-page .unread {
  color: red;
  font-weight: bold;
}/*# sourceMappingURL=notification.css.map */