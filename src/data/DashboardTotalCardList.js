import {
  Timer02Icon,
  BedIcon,
  SearchingIcon,
  WorkoutRunIcon,
} from "hugeicons-react";
export const DashboardTotalCardList = [
  {
    id: "acute",
    name: "Total Acute",
    icon: <Timer02Icon size={24} color={"#07AEEF"} variant={"stroke"} />,
    value: 34,
  },
  {
    id: "swing-bed",
    name: "Total Swing Bed",
    icon: <BedIcon size={24} color={"#FFB60A"} variant={"stroke"} />,
    value: 56,
  },
  {
    id: "observation",
    name: "Total Observation",
    icon: <SearchingIcon size={24} color={"#FC7D75"} variant={"stroke"} />,
    value: 23,
  },
  {
    id: "emergency",
    name: "Total Emergency Room",
    icon: <WorkoutRunIcon size={24} color={"#0E76BC"} variant={"stroke"} />,
    value: 78,
  },
];

const totals = [
  {
    "year": 2022,
    "data": [
      {
        "month": "Jan",
        "data": [
          { "acute": 0.9876 },
          { "emergency": 0.5432 },
          { "observation": 0.6789 },
          { "swing_bed": 0.1234 }
        ]
      },
      {
        "month": "Feb",
        "data": [
          { "acute": 0.2345 },
          { "emergency": 0.6789 },
          { "observation": 0.5432 },
          { "swing_bed": 0.9876 }
        ]
      },
      {
        "month": "Mar",
        "data": [
          { "acute": 0.4321 },
          { "emergency": 0.8765 },
          { "observation": 0.3210 },
          { "swing_bed": 0.6543 }
        ]
      },
      {
        "month": "Apr",
        "data": [
          { "acute": 0.7654 },
          { "emergency": 0.2109 },
          { "observation": 0.0987 },
          { "swing_bed": 0.5432 }
        ]
      },
      {
        "month": "May",
        "data": [
          { "acute": 0.8765 },
          { "emergency": 0.4321 },
          { "observation": 0.2109 },
          { "swing_bed": 0.6543 }
        ]
      },
      {
        "month": "Jun",
        "data": [
          { "acute": 0.5432 },
          { "emergency": 0.6543 },
          { "observation": 0.7654 },
          { "swing_bed": 0.8765 }
        ]
      },
      {
        "month": "Jul",
        "data": [
          { "acute": 0.4321 },
          { "emergency": 0.5432 },
          { "observation": 0.6543 },
          { "swing_bed": 0.7654 }
        ]
      },
      {
        "month": "Aug",
        "data": [
          { "acute": 0.2109 },
          { "emergency": 0.3210 },
          { "observation": 0.4321 },
          { "swing_bed": 0.5432 }
        ]
      },
      {
        "month": "Sep",
        "data": [
          { "acute": 0.3210 },
          { "emergency": 0.4321 },
          { "observation": 0.5432 },
          { "swing_bed": 0.6543 }
        ]
      },
      {
        "month": "Oct",
        "data": [
          { "acute": 0.6543 },
          { "emergency": 0.7654 },
          { "observation": 0.8765 },
          { "swing_bed": 0.9876 }
        ]
      },
      {
        "month": "Nov",
        "data": [
          { "acute": 0.7654 },
          { "emergency": 0.8765 },
          { "observation": 0.9876 },
          { "swing_bed": 0.0987 }
        ]
      },
      {
        "month": "Dec",
        "data": [
          { "acute": 0.5432 },
          { "emergency": 0.6543 },
          { "observation": 0.7654 },
          { "swing_bed": 0.8765 }
        ]
      }
    ]
  },
  {
    "year": 2023,
    "data": [
      {
        "month": "Jan",
        "data": [
          { "acute": 0.8765 },
          { "emergency": 0.5432 },
          { "observation": 0.4321 },
          { "swing_bed": 0.3210 }
        ]
      },
      {
        "month": "Feb",
        "data": [
          { "acute": 0.1234 },
          { "emergency": 0.9876 },
          { "observation": 0.8765 },
          { "swing_bed": 0.7654 }
        ]
      },
      {
        "month": "Mar",
        "data": [
          { "acute": 0.6543 },
          { "emergency": 0.5432 },
          { "observation": 0.4321 },
          { "swing_bed": 0.3210 }
        ]
      },
      {
        "month": "Apr",
        "data": [
          { "acute": 0.2109 },
          { "emergency": 0.0987 },
          { "observation": 0.8765 },
          { "swing_bed": 0.7654 }
        ]
      },
      {
        "month": "May",
        "data": [
          { "acute": 0.6543 },
          { "emergency": 0.5432 },
          { "observation": 0.4321 },
          { "swing_bed": 0.3210 }
        ]
      },
      {
        "month": "Jun",
        "data": [
          { "acute": 0.2109 },
          { "emergency": 0.0987 },
          { "observation": 0.9876 },
          { "swing_bed": 0.8765 }
        ]
      },
      {
        "month": "Jul",
        "data": [
          { "acute": 0.7654 },
          { "emergency": 0.6543 },
          { "observation": 0.5432 },
          { "swing_bed": 0.4321 }
        ]
      },
      {
        "month": "Aug",
        "data": [
          { "acute": 0.3210 },
          { "emergency": 0.2109 },
          { "observation": 0.0987 },
          { "swing_bed": 0.9876 }
        ]
      },
      {
        "month": "Sep",
        "data": [
          { "acute": 0.8765 },
          { "emergency": 0.7654 },
          { "observation": 0.6543 },
          { "swing_bed": 0.5432 }
        ]
      },
      {
        "month": "Oct",
        "data": [
          { "acute": 0.4321 },
          { "emergency": 0.3210 },
          { "observation": 0.2109 },
          { "swing_bed": 0.0987 }
        ]
      },
      {
        "month": "Nov",
        "data": [
          { "acute": 0.5432 },
          { "emergency": 0.6543 },
          { "observation": 0.7654 },
          { "swing_bed": 0.8765 }
        ]
      },
      {
        "month": "Dec",
        "data": [
          { "acute": 0.9876 },
          { "emergency": 0.8765 },
          { "observation": 0.7654 },
          { "swing_bed": 0.6543 }
        ]
      }
    ]
  },
  {
    "year": 2024,
    "data": [
      {
        "month": "Jan",
        "data": [
          { "acute": 0.1234 },
          { "emergency": 0.2345 },
          { "observation": 0.3456 },
          { "swing_bed": 0.4567 }
        ]
      },
      {
        "month": "Feb",
        "data": [
          { "acute": 0.5678 },
          { "emergency": 0.6789 },
          { "observation": 0.7890 },
          { "swing_bed": 0.8901 }
        ]
      },
      {
        "month": "Mar",
        "data": [
          { "acute": 0.9012 },
          { "emergency": 0.0123 },
          { "observation": 0.1234 },
          { "swing_bed": 0.2345 }
        ]
      },
      {
        "month": "Apr",
        "data": [
          { "acute": 0.3456 },
          { "emergency": 0.4567 },
          { "observation": 0.5678 },
          { "swing_bed": 0.6789 }
        ]
      },
      {
        "month": "May",
        "data": [
          { "acute": 0.7890 },
          { "emergency": 0.8901 },
          { "observation": 0.9012 },
          { "swing_bed": 0.0123 }
        ]
      },
      {
        "month": "Jun",
        "data": [
          { "acute": 0.2345 },
          { "emergency": 0.3456 },
          { "observation": 0.4567 },
          { "swing_bed": 0.5678 }
        ]
      },
      {
        "month": "Jul",
        "data": [
          { "acute": 0.6789 },
          { "emergency": 0.7890 },
          { "observation": 0.8901 },
          { "swing_bed": 0.9012 }
        ]
      },
      {
        "month": "Aug",
        "data": [
          { "acute": 0.0123 },
          { "emergency": 0.1234 },
          { "observation": 0.2345 },
          { "swing_bed": 0.3456 }
        ]
      },
      {
        "month": "Sep",
        "data": [
          { "acute": 0.4567 },
          { "emergency": 0.5678 },
          { "observation": 0.6789 },
          { "swing_bed": 0.7890 }
        ]
      },
      {
        "month": "Oct",
        "data": [
          { "acute": 0.8901 },
          { "emergency": 0.9012 },
          { "observation": 0.0123 },
          { "swing_bed": 0.1234 }
        ]
      },
      {
        "month": "Nov",
        "data": [
          { "acute": 0.2345 },
          { "emergency": 0.3456 },
          { "observation": 0.4567 },
          { "swing_bed": 0.5678 }
        ]
      },
      {
        "month": "Dec",
        "data": [
          { "acute": 0.6789 },
          { "emergency": 0.7890 },
          { "observation": 0.8901 },
          { "swing_bed": 0.9012 }
        ]
      }
    ]
  }
]

const chart = [
  {
    'year': '2024',
    "measures": [
      {
        "name": 'Left without seen',
        "hospitals": [
          {
            'name': 'Mangum',
            "months": [
              { "jan": 1.2 },
              { "feb": 0.8 },
              { "mar": 1.3 },
              { "apr": 0.7 },
              { "may": 1.1 },
              { "jun": 0.9 },
              { "jul": 1.4 },
              { "aug": 0.6 },
              { "sep": 1.2 },
              { "oct": 0.8 },
              { "nov": 1.0 },
              { "dec": 1.3 }
            ]
          },
          {
            'name': 'Prague',
            "months": [
              { "jan": 0.9 },
              { "feb": 1.1 },
              { "mar": 0.8 },
              { "apr": 1.3 },
              { "may": 0.7 },
              { "jun": 1.2 },
              { "jul": 0.9 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.0 },
              { "nov": 1.1 },
              { "dec": 0.9 }
            ]
          },
          {
            'name': 'Pawhuska',
            "months": [
              { "jan": 1.1 },
              { "feb": 0.9 },
              { "mar": 1.2 },
              { "apr": 0.8 },
              { "may": 1.0 },
              { "jun": 1.3 },
              { "jul": 0.7 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.1 },
              { "nov": 1.2 },
              { "dec": 0.9 }
            ]
          }, {
            'name': 'Seilling',
            "months": [
              { "jan": 0.8 },
              { "feb": 1.0 },
              { "mar": 1.2 },
              { "apr": 0.9 },
              { "may": 1.1 },
              { "jun": 1.3 },
              { "jul": 0.7 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.0 },
              { "nov": 1.1 },
              { "dec": 0.9 }
            ]
          },
          {
            'name': 'Carnegie',
            "months": [
              { "jan": 0.7 },
              { "feb": 1.1 },
              { "mar": 1.3 },
              { "apr": 0.9 },
              { "may": 1.0 },
              { "jun": 1.2 },
              { "jul": 0.8 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.1 },
              { "nov": 1.2 },
              { "dec": 0.9 }
            ]
          }
        ],
      },
      {
        "name": 'Acute',
        "hospitals": [
          {
            'name': 'Mangum',
            "months": [
              { "jan": 1.2 },
              { "feb": 0.8 },
              { "mar": 1.3 },
              { "apr": 0.7 },
              { "may": 1.1 },
              { "jun": 0.9 },
              { "jul": 1.4 },
              { "aug": 0.6 },
              { "sep": 1.2 },
              { "oct": 0.8 },
              { "nov": 1.0 },
              { "dec": 1.3 }
            ]
          },
          {
            'name': 'Prague',
            "months": [
              { "jan": 0.9 },
              { "feb": 1.1 },
              { "mar": 0.8 },
              { "apr": 1.3 },
              { "may": 0.7 },
              { "jun": 1.2 },
              { "jul": 0.9 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.0 },
              { "nov": 1.1 },
              { "dec": 0.9 }
            ]
          },
          {
            'name': 'Pawhuska',
            "months": [
              { "jan": 1.1 },
              { "feb": 0.9 },
              { "mar": 1.2 },
              { "apr": 0.8 },
              { "may": 1.0 },
              { "jun": 1.3 },
              { "jul": 0.7 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.1 },
              { "nov": 1.2 },
              { "dec": 0.9 }
            ]
          }, {
            'name': 'Seilling',
            "months": [
              { "jan": 0.8 },
              { "feb": 1.0 },
              { "mar": 1.2 },
              { "apr": 0.9 },
              { "may": 1.1 },
              { "jun": 1.3 },
              { "jul": 0.7 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.0 },
              { "nov": 1.1 },
              { "dec": 0.9 }
            ]
          },
          {
            'name': 'Carnegie',
            "months": [
              { "jan": 0.7 },
              { "feb": 1.1 },
              { "mar": 1.3 },
              { "apr": 0.9 },
              { "may": 1.0 },
              { "jun": 1.2 },
              { "jul": 0.8 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.1 },
              { "nov": 1.2 },
              { "dec": 0.9 }
            ]
          }
        ],

      }
    ]
  },

  {
    'year': '2024',
    "measures": [
      {
        "name": 'Left without seen',
        "hospitals": [
          {
            'name': 'Mangum',
            "months": [
              { "jan": 1.2 },
              { "feb": 0.8 },
              { "mar": 1.3 },
              { "apr": 0.7 },
              { "may": 1.1 },
              { "jun": 0.9 },
              { "jul": 1.4 },
              { "aug": 0.6 },
              { "sep": 1.2 },
              { "oct": 0.8 },
              { "nov": 1.0 },
              { "dec": 1.3 }
            ]
          },
          {
            'name': 'Prague',
            "months": [
              { "jan": 0.9 },
              { "feb": 1.1 },
              { "mar": 0.8 },
              { "apr": 1.3 },
              { "may": 0.7 },
              { "jun": 1.2 },
              { "jul": 0.9 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.0 },
              { "nov": 1.1 },
              { "dec": 0.9 }
            ]
          },
          {
            'name': 'Pawhuska',
            "months": [
              { "jan": 1.1 },
              { "feb": 0.9 },
              { "mar": 1.2 },
              { "apr": 0.8 },
              { "may": 1.0 },
              { "jun": 1.3 },
              { "jul": 0.7 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.1 },
              { "nov": 1.2 },
              { "dec": 0.9 }
            ]
          }, {
            'name': 'Seilling',
            "months": [
              { "jan": 0.8 },
              { "feb": 1.0 },
              { "mar": 1.2 },
              { "apr": 0.9 },
              { "may": 1.1 },
              { "jun": 1.3 },
              { "jul": 0.7 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.0 },
              { "nov": 1.1 },
              { "dec": 0.9 }
            ]
          },
          {
            'name': 'Carnegie',
            "months": [
              { "jan": 0.7 },
              { "feb": 1.1 },
              { "mar": 1.3 },
              { "apr": 0.9 },
              { "may": 1.0 },
              { "jun": 1.2 },
              { "jul": 0.8 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.1 },
              { "nov": 1.2 },
              { "dec": 0.9 }
            ]
          }
        ],

      },
      {
        "name": 'Acute',
        "hospitals": [
          {
            'name': 'Mangum',
            "months": [
              { "jan": 1.2 },
              { "feb": 0.8 },
              { "mar": 1.3 },
              { "apr": 0.7 },
              { "may": 1.1 },
              { "jun": 0.9 },
              { "jul": 1.4 },
              { "aug": 0.6 },
              { "sep": 1.2 },
              { "oct": 0.8 },
              { "nov": 1.0 },
              { "dec": 1.3 }
            ]
          },
          {
            'name': 'Prague',
            "months": [
              { "jan": 0.9 },
              { "feb": 1.1 },
              { "mar": 0.8 },
              { "apr": 1.3 },
              { "may": 0.7 },
              { "jun": 1.2 },
              { "jul": 0.9 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.0 },
              { "nov": 1.1 },
              { "dec": 0.9 }
            ]
          },
          {
            'name': 'Pawhuska',
            "months": [
              { "jan": 1.1 },
              { "feb": 0.9 },
              { "mar": 1.2 },
              { "apr": 0.8 },
              { "may": 1.0 },
              { "jun": 1.3 },
              { "jul": 0.7 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.1 },
              { "nov": 1.2 },
              { "dec": 0.9 }
            ]
          }, {
            'name': 'Seilling',
            "months": [
              { "jan": 0.8 },
              { "feb": 1.0 },
              { "mar": 1.2 },
              { "apr": 0.9 },
              { "may": 1.1 },
              { "jun": 1.3 },
              { "jul": 0.7 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.0 },
              { "nov": 1.1 },
              { "dec": 0.9 }
            ]
          },
          {
            'name': 'Carnegie',
            "months": [
              { "jan": 0.7 },
              { "feb": 1.1 },
              { "mar": 1.3 },
              { "apr": 0.9 },
              { "may": 1.0 },
              { "jun": 1.2 },
              { "jul": 0.8 },
              { "aug": 1.4 },
              { "sep": 0.6 },
              { "oct": 1.1 },
              { "nov": 1.2 },
              { "dec": 0.9 }
            ]
          }
        ],

      }]
  },

]

const performanceMeasures = [
  {
    "year": 2023,
    "measures": [
      {
        "name": "Pressure ulcer",
        "BM": 24,
        "hospitals": [
          { "name": "Mangum", "value": 0.8 },
          { "name": "Prague", "value": 0.7 },
          { "name": "Pawhuska", "value": 0.9 },
          { "name": "Seilling", "value": 0.6 },
          { "name": "Carnegie", "value": 0.7 }
        ]
      },
      {
        "name": "Acute",
        "BM": 24,
        "hospitals": [
          { "name": "Mangum", "value": 0.8 },
          { "name": "Prague", "value": 0.7 },
          { "name": "Pawhuska", "value": 0.9 },
          { "name": "Seilling", "value": 0.6 },
          { "name": "Carnegie", "value": 0.7 }
        ]
      }
    ]
  },
  {
    "year": 2024,
    "measures": [
      {
        "name": "Pressure ulcer",
        "BM": 24,
        "hospitals": [
          { "name": "Mangum", "value": 0.8 },
          { "name": "Prague", "value": 0.7 },
          { "name": "Pawhuska", "value": 0.9 },
          { "name": "Seilling", "value": 0.6 },
          { "name": "Carnegie", "value": 0.7 }
        ]
      },
      {
        "name": "Acute",
        "BM": 24,
        "hospitals": [
          { "name": "Mangum", "value": 0.8 },
          { "name": "Prague", "value": 0.7 },
          { "name": "Pawhuska", "value": 0.9 },
          { "name": "Seilling", "value": 0.6 },
          { "name": "Carnegie", "value": 0.7 }
        ]
      }
    ]
  }
]

const emergencyRooms = [
  {
    "year": 2023,
    "total": 100,
    "hospitals": [
      { "name": "Mangum", "value": 30 },
      { "name": "Prague", "value": 25 },
      { "name": "Pawhuska", "value": 28 },
      { "name": "Seilling", "value": 32 },
      { "name": "Carnegie", "value": 27 }
    ]
  },
  {
    "year": 2024,
    "total": 35,
    "hospitals": [
      { "name": "Mangum", "value": 32 },
      { "name": "Prague", "value": 27 },
      { "name": "Pawhuska", "value": 30 },
      { "name": "Seilling", "value": 34 },
      { "name": "Carnegie", "value": 29 }
    ]
  }
]