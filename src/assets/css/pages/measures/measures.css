.measures-page {
  display: grid;
  height: -moz-fit-content;
  height: fit-content;
  gap: 1rem;
}
.measures-page .loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(23, 23, 23, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.measures-page .spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.measures-page .loading-overlay p {
  color: white;
  font-size: 16px;
}
.measures-page .search-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  background-color: white;
  padding: 1rem;
  border-radius: 0.5rem;
}
.measures-page .search-filter .filters {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}
.measures-page .search-filter .filters select {
  padding: 12px;
  outline: none;
  border: 1px solid #eee;
  border-radius: 0.8rem;
  color: gray;
}
.measures-page .search-filter .filters .field {
  width: 60%;
}
.measures-page .search-filter .years-dropdown {
  display: flex;
  gap: 1rem;
}
.measures-page .search-filter .action {
  display: flex;
  gap: 1rem;
}
.measures-page .search-filter .edit-icon {
  background-color: rgb(57, 94, 57);
}
.measures-page .measures {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  overflow: auto;
}
.measures-page .pagination {
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 20px;
}

.new-measure-form {
  overflow-y: auto;
}

@media screen and (max-width: 845px) {
  .content .measures-page .search-filter {
    flex-direction: column;
  }
  .content .measures-page .search-filter .years-dropdown {
    display: flex;
    width: 100%;
    gap: 12px;
  }
  .content .measures-page .search-filter .years-dropdown .year-select {
    width: 100%;
  }
}
@media screen and (max-width: 600px) {
  .measures-page .search-filter .filters {
    flex-direction: column;
  }
  .measures-page .search-filter .filters .field {
    width: 100%;
  }
}
.date-error {
  color: red;
}/*# sourceMappingURL=measures.css.map */