import React, { useState, useRef, useEffect } from 'react';
import { TextInput, DataListInput, YearInput } from '../Input';
import { PrimaryButton, SecondaryButton } from '../buttons';
import toast from 'react-hot-toast';
import { Flag01Icon } from "hugeicons-react";
import api, { API_URL } from '../../../api';
import Cookies from 'js-cookie';
import dayjs from 'dayjs';
import ConfirmPopup from '../ConfirmMeasurePopup';
import moment from 'moment/moment';
import '../../../assets/css/pages/measures/popupmeasure.css';

const currentYear = dayjs();

const NewMeasureForm = ({ isOpen, onClose, onMeasureAdded }) => {
    const [name, setName] = useState('');
    const [synchronous_id, setSynchronous_id] = useState('');
    const [measure_year, setMeasureYear] = useState(currentYear);
    const [category, setCategory] = useState('');
    const [group, setGroup] = useState('');
    const [categories, setCategories] = useState([]);
    const [groups, setGroups] = useState([]);
    const [type, setType] = useState('');
    const [description, setDescription] = useState('');
    const [typeOptions] = useState(['numerical', 'percentage']);
    const [isSaving, setIsSaving] = useState(false);
    const [measureExistsMessage, setMeasureExistsMessage] = useState("");
    const [showConfirmPopup, setShowConfirmPopup] = useState(false);
    const [measureIdConfirmed, setMeasureIdConfirmed] = useState(false);
    const [isCheckingId, setIsCheckingId] = useState(false);
    const popupRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

    useEffect(() => {
        if (isOpen) {
            setName('');
            setSynchronous_id('');
            setCategory('');
            setType('');
            setDescription('');
            setMeasureYear(currentYear);
            setMeasureExistsMessage("");
            setMeasureIdConfirmed(false);
        }
    }, [isOpen]);

    const fetchCategories = async () => {
        try {

            const response = await api.get(`${API_URL}/categories/list/`);
            const categoryNames = response.data.map(category => category.name);

            setCategories(categoryNames);
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.error('Failed to fetch categories');
        }
    };

    const fetchMeasureGroups = async () => {
        try {

            const response = await api.get(`${API_URL}/measures/measure-groups/`);
            const groups = response.data["measure_group_names"]

            setGroups(groups);
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.error('Failed to fetch categories');
        }
    };

    useEffect(() => {


        fetchCategories();
        fetchMeasureGroups()
    }, []);

    const checkMeasureIdExists = async (id) => {
        try {
            if (!id) return false;

            const token = Cookies.get('accessToken');
            const formattedYear = moment(measure_year).format('YYYY');

            console.log(`Checking if measure ID ${id} exists for year ${formattedYear}`);

          
            const idStr = id.toString();

            const response = await api.get(`${API_URL}/measures/synchronous_id/`, {
                params: {
                    synchronous_id: idStr,
                    measure_year: formattedYear
                },
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            console.log('API response:', response.data);

            if (response.data.is_synchronous_id_exist) {
                if (response.data.same_year_exist) {
                    setMeasureExistsMessage(
                        response.data.same_year_message ||
                        `This Measure ID (${id}) is already in use for the current year. Please use a different ID.`
                    );
                   
                    return false;
                } else {
                   
                    return true;
                }
            } else {
                
                setMeasureExistsMessage("");
                return false;
            }
        } catch (error) {
            console.error('Error checking measure ID:', error);
            toast.error('Error checking if measure ID exists');
            return false;
        }
    };

  
    const [typingTimeout, setTypingTimeout] = useState(null);

    const handleSynchronousIdChange = (value) => {
        console.log("handleSynchronousIdChange called with value:", value);

        
        setSynchronous_id(value);

       
        setMeasureIdConfirmed(false);
        setMeasureExistsMessage("");
        setShowConfirmPopup(false);

      
        if (typingTimeout) {
            clearTimeout(typingTimeout);
        }

       
        if (value) {
           
            const newTimeout = setTimeout(async () => {
                try {
                    console.log("User finished typing, checking ID:", value);
                    setIsCheckingId(true);

                    const exists = await checkMeasureIdExists(value);
                    console.log("ID exists check result:", exists);

                    if (exists) {
                        console.log("Setting showConfirmPopup to true");
                       
                        setMeasureExistsMessage(
                            `This Measure ID (${value}) has been used in previous reporting periods. Would you like to continue using it?`
                        );
                        setShowConfirmPopup(true);
                    } else {
                        console.log("Setting measureIdConfirmed to true");
                        setMeasureIdConfirmed(true);
                    }
                } catch (error) {
                    console.error("Error in handleSynchronousIdChange:", error);
                } finally {
                    setIsCheckingId(false);
                }
            }, 1000); 

            setTypingTimeout(newTimeout);
        }
    };

    const handleConfirm = () => {
        
        setMeasureIdConfirmed(true);
        setShowConfirmPopup(false);
       
        setMeasureExistsMessage("");
        console.log("User confirmed using measure ID:", synchronous_id);
    };

    const handleCancel = () => {
        setShowConfirmPopup(false);
        setSynchronous_id("");
        setMeasureExistsMessage("");
        setMeasureIdConfirmed(false);
    };

    const handleNewMeasureForm = async (event) => {
        event.preventDefault();
        console.log("Form submission with state:", {
            measureIdConfirmed,
            showConfirmPopup,
            synchronous_id,
            measureExistsMessage
        });

        if (showConfirmPopup) {
            toast.error("Please confirm if you want to use this Measure ID by clicking 'Yes, Use This ID' or 'Cancel'.");
            return;
        }
        if (!measureIdConfirmed && synchronous_id && measureExistsMessage) {
            toast.error("Please confirm the Measure ID or choose a different one.");
            return;
        }

        if (!name || !category || !synchronous_id || !measure_year || !description || !type) {
            toast.error('Please fill all required fields');
            return;
        }

        // Validate measure ID format
        if (!/^\d{6}$/.test(synchronous_id)) {
            toast.error('Measure ID must be exactly 6 numeric digits');
            return;
        }

        const transformYear = measure_year.format('YYYY');
        const newMeasure = {
            name,
            synchronous_id,
            category,
            description,
            group,
            "measure_unit": type,
            "measure_year": transformYear
        };

        setIsSaving(true);

        try {
            const token = Cookies.get('accessToken');
            console.log("Submitting measure data:", newMeasure);

            const confirmedMeasure = {
                ...newMeasure,
                confirmed_id: measureIdConfirmed 
            };

            console.log("Submitting with confirmed ID:", confirmedMeasure);

            const response = await api.post(`${API_URL}/measures/new_measure/`, confirmedMeasure, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 201) {
                toast.success('Measure added successfully');

                
                if (typeof onMeasureAdded === 'function') {
                    onMeasureAdded(response.data);
                }

                setName('');
                setSynchronous_id('');
                setCategory('');
                setType('');
                setDescription('');
                setMeasureYear(currentYear);
                setMeasureExistsMessage("");
                setMeasureIdConfirmed(false);

                onClose();

                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                toast.error('Unexpected response. Measure may not have been saved.');
            }
        } catch (error) {
            console.error('Error adding measure:', error);
            if (error.response && error.response.data && error.response.data.error) {
                toast.error(error.response.data.error);
            } else {
                toast.error("An error occurred while saving.");
            }
        } finally {
            setIsSaving(false);
        }
    };


    return (
        <div className={`form ${isOpen ? 'open' : ''}`} ref={popupRef}>
            <h2>New Measure</h2>

            <div className="measure-instructions">
                <h3>Measure Rules:</h3>
                <ul>
                    <li>Create a unique measure ID. Reusing an existing measure ID is not allowed</li>
                    <li>Numeric digits only</li>
                    <li>Length must be six digits</li>
                </ul>
            </div>

            {showConfirmPopup && (
                <ConfirmPopup
                    message={`The Measure ID "${synchronous_id}" is already in use from a previous reporting period. Would you like to continue using this ID?`}
                    onConfirm={handleConfirm}
                    onCancel={handleCancel}
                    showConfirmPopup={showConfirmPopup}
                />
            )}

            <form onSubmit={handleNewMeasureForm}>
                <TextInput
                    type='text'
                    name='measureName'
                    placeholder='Measure name'
                    value={name}
                    setValue={setName}
                />

                <div>
                    <label htmlFor="measureID">This field only accepts numerical values (6 digits required).</label>
                    <TextInput
                        type="number"
                        name="measureID"
                        placeholder="Measure ID (6 digits)"
                        value={synchronous_id}
                        setValue={handleSynchronousIdChange}
                        maxLength={6}
                        pattern="[0-9]{6}"
                    />
                    {measureExistsMessage && (
                        <p className="text-red">{measureExistsMessage}</p>
                    )}
                </div>

                <textarea
                    className='text-area-measure'
                    name='description'
                    placeholder='Measure Descriptions'
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                ></textarea>

                <div className='value-type'>
                <DataListInput
                    iconClass={<Flag01Icon />}
                    id="category"
                    name="categoryName"
                    placeholder="Category Name"
                    value={category}
                    setValue={setCategory}
                    options={categories}
                />

                <DataListInput
                    iconClass={<Flag01Icon />}
                    id="group"
                    name="groupName"
                    placeholder="Group Name"
                    value={group}
                    setValue={setGroup}
                    options={groups}
                />

                <DataListInput
                    id="type"
                    name="measureType"
                    placeholder="Select Type"
                    value={type}
                    options={typeOptions}
                    setValue={setType}
                />
                </div>

                <div
                    onMouseDown={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                >
                    <YearInput
                        year={measure_year}
                        setYear={setMeasureYear}
                    />
                </div>

                <div className="buttons">
                      <SecondaryButton
                        onClick={onClose}
                        isLoading={false}
                        buttonText="Cancel"
                    />
                    <PrimaryButton
                        buttonText="Save"
                        type="submit"
                        isLoading={isSaving}
                        processingText="Saving"
                    />
                  
                </div>
            </form>
        </div>
    );
};

export default NewMeasureForm;
