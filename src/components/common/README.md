# Standardized Search and Filter Components

This directory contains standardized search and filter components that provide a consistent design across the application, matching the design specification with a clean search input and blue search button.

## Components Overview

### 1. StandardSearchBar
A reusable search component with consistent styling.

**Features:**
- Clean input field with blue search button
- Loading states
- Keyboard support (Enter to search)
- Responsive design
- Multiple size variants

**Usage:**
```jsx
import StandardSearchBar from '../components/common/StandardSearchBar';

<StandardSearchBar
    placeholder="Search by name"
    value={searchQuery}
    onChange={setSearchQuery}
    onSearch={handleSearch}
    isLoading={isSearching}
    className="compact" // optional: compact, full-width
/>
```

### 2. StandardFilters
A container component for organizing filter controls.

**Features:**
- Consistent filter styling
- Reset functionality
- Responsive layout
- Multiple layout options

**Usage:**
```jsx
import StandardFilters, { FilterSelect, FilterInput, FilterDateRange } from '../components/common/StandardFilters';

<StandardFilters
    onReset={handleResetFilters}
    showReset={hasFilters}
    title="Filter Options"
>
    <FilterSelect
        label="Category"
        value={selectedCategory}
        onChange={setSelectedCategory}
        options={categories}
        placeholder="All Categories"
    />
    <FilterInput
        label="Name"
        value={nameFilter}
        onChange={setNameFilter}
        placeholder="Filter by name"
    />
    <FilterDateRange
        label="Date Range"
        startDate={startDate}
        endDate={endDate}
        onStartDateChange={setStartDate}
        onEndDateChange={setEndDate}
    />
</StandardFilters>
```

### 3. SearchAndFilters
A combined component that includes both search and filters in various layouts.

**Features:**
- Multiple layout options (stacked, inline, side-by-side)
- Integrated search and filter functionality
- Responsive design
- Consistent spacing and styling

**Usage:**
```jsx
import SearchAndFilters from '../components/common/SearchAndFilters';
import { FilterSelect } from '../components/common/StandardFilters';

<SearchAndFilters
    searchValue={searchQuery}
    onSearchChange={setSearchQuery}
    onSearch={handleSearch}
    searchPlaceholder="Search measures..."
    isSearching={isSearching}
    onResetFilters={handleResetFilters}
    showResetFilters={hasFilters}
    layout="inline" // stacked, inline, side-by-side
>
    <FilterSelect
        label="Year"
        value={selectedYear}
        onChange={setSelectedYear}
        options={yearOptions}
    />
    <FilterSelect
        label="Hospital"
        value={selectedHospital}
        onChange={setSelectedHospital}
        options={hospitalOptions}
        placeholder="All Hospitals"
    />
</SearchAndFilters>
```

## Layout Options

### Stacked Layout (Default)
Search bar on top, filters below in a separate container.

### Inline Layout
Search bar and filters in the same container, arranged horizontally.

### Side-by-Side Layout
Search bar and filters in separate columns (responsive - stacks on mobile).

## Styling Classes

### StandardSearchBar Classes
- `.compact` - Smaller max-width (400px)
- `.full-width` - Takes full available width
- `.small` - Smaller padding and font size

### StandardFilters Classes
- `.compact` - Reduced padding
- `.no-border` - Removes border and background
- `.inline` - Optimized for inline layouts

## Examples from Updated Pages

### TargetPage
```jsx
<StandardSearchBar
    placeholder="Search by measure name"
    value={searchQuery}
    onChange={handleSearchChange}
    onSearch={handleSearchAction}
    isLoading={isSearching}
/>
```

### MeasureDataPage
```jsx
<SearchAndFilters
    searchValue={searchQuery}
    onSearchChange={handleInputChange}
    onSearch={() => handleSearch(searchQuery)}
    searchPlaceholder="Search by measure name"
    isSearching={isSearching}
    onResetFilters={handleClearSearch}
    showResetFilters={hasSearched}
    layout="inline"
>
    <FilterSelect label="Year" value={selectedYear} onChange={handleDurationYearChange} options={yearOptions} />
    <FilterSelect label="Hospital" value={selectedHospital} onChange={setSelectedHospital} options={hospitalOptions} />
    <FilterSelect label="Month" value={selectedMonth} onChange={setSelectedMonth} options={monthOptions} />
</SearchAndFilters>
```

## Migration Guide

To update existing search components:

1. **Replace TextInput with Search Icon** → `StandardSearchBar`
2. **Replace custom filter containers** → `StandardFilters`
3. **Combine search + filters** → `SearchAndFilters`

## Benefits

- ✅ Consistent design across all pages
- ✅ Responsive and accessible
- ✅ Reduced code duplication
- ✅ Easy to maintain and update
- ✅ Matches design specifications
- ✅ Built-in loading states and interactions
