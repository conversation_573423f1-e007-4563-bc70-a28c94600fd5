import React, { useRef, useEffect } from 'react';
import { PrimaryButton, SecondaryButton } from '../buttons';
import { PlusSignIcon, Copy01Icon } from 'hugeicons-react';
import '../../../assets/css/components/forms/measure-action-dialog.css';

const MeasureActionDialog = ({ isOpen, onClose, onCreateNew, onCopyMeasures }) => {
    const dialogRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (dialogRef.current && !dialogRef.current.contains(e.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    return (
        <div className="measure-action-overlay">
            <div className="measure-action-dialog" ref={dialogRef}>
                <h2>Choose Action</h2>
                <p>What would you like to do?</p>
                
                <div className="action-options">
                    <div className="action-option">
                        <div className="option-icon create-icon">
                            <PlusSignIcon size={24} />
                        </div>
                        <div className="option-content">
                            <h3>Create New Measure</h3>
                            <p>Create a brand new measure from scratch</p>
                        </div>
                        <PrimaryButton
                            buttonText="Create New"
                            onClick={onCreateNew}
                            iconClass={<PlusSignIcon />}
                        />
                    </div>

                    <div className="action-option">
                        <div className="option-icon copy-icon">
                            <Copy01Icon size={24} />
                        </div>
                        <div className="option-content">
                            <h3>Copy Measures</h3>
                            <p>Copy existing measures to another year</p>
                        </div>
                        <SecondaryButton
                            buttonText="Copy Measures"
                            onClick={onCopyMeasures}
                            iconClass={<Copy01Icon />}
                        />
                    </div>
                </div>

                <div className="dialog-actions">
                    <SecondaryButton
                        buttonText="Cancel"
                        onClick={onClose}
                    />
                </div>
            </div>
        </div>
    );
};

export default MeasureActionDialog;
