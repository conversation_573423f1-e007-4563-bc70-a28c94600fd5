import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';
import { API_URL } from '../../api';
import { PrimaryButton, SecondaryButton } from '../../components/forms/buttons';
import { DataListInput, TextInput } from '../../components/forms/Input';
import ViewPositionDetailsPopup from '../positionPopup/ViewPositionDetailsPopup';

const AddUserForm = ({ isOpen, onClose, onUserAdded }) => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [permission, setPermission] = useState(null);
  const [permissions, setPermissions] = useState([]);
  const [positions, setPositions] = useState([]);
  const [hospital, setHospital] = useState([]);
  const [hospitals, setHospitals] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState('');


  const popupRef = useRef(null);

  useEffect(() => {

    const matchedPermission = permissions.find(
      (permission) => permission.name === selectedPosition
    );
    if (matchedPermission) {

      setPermission(matchedPermission);

    }

  }, [selectedPosition]);

  useEffect(() => {
    const fetchHospitals = async () => {
      try {
        const token = Cookies.get('accessToken');
        const response = await axios.get(`${API_URL}/hospitals/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        const hospitalsData = response.data;
        const hospitalNames = hospitalsData.map(hospital => hospital.name);
        setHospitals(hospitalNames);

      } catch (error) {
        console.error('Error fetching hospitals:', error);
        toast.error('Failed to load hospitals data');
      }
    };

    fetchHospitals();
  }, []);




  useEffect(() => {
    const fetchPositions = async () => {
      try {
        const token = Cookies.get('accessToken');
        const response = await axios.get(`${API_URL}/position/`
          , {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          }
        );
        setPositions(response.data.map(position => position.name));
        setPermissions(response.data)
      } catch (error) {
        console.error('Error fetching Roles:', error);
        toast.error('Failed to fetch Roles');
      }
    };
    fetchPositions();
  }, []);

  const handleClickOutside = (event) => {
    if (popupRef.current && !popupRef.current.contains(event.target)) {
      onClose();
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);


  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!hospital || !selectedPosition || !username || !email || !firstName || !lastName) {
      toast.error('Please fill in all required fields');
      setIsSubmitting(false);
      return;
    }

    try {

      const newUser = {
        users: [{
          username,
          email,
          first_name: firstName,
          last_name: lastName,
          hospital: hospital,
          position: selectedPosition,
        }],
        permission,
      };
      console.log(newUser)

      const token = Cookies.get('accessToken');
      setIsLoading(true);


      const response = await axios.post(`${API_URL}/user/new_user/`, newUser, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      console.log(response)
      if (response.status >= 200 && response.status < 300) {
        toast.success("User successfully added");
        if (typeof onUserAdded === 'function') {
          onUserAdded(response.data);
        }
        onClose();
      } else {
        toast.error("Unexpected response. User may not have been saved.");
      }
    } catch (error) {
      console.error('Error while adding user:', error);
      if (error.response) {
        toast.error('Error while adding user: ' + (error.response.data.error || 'Unknown error'));
      } else {
        toast.error('No response received from server');
      }
    } finally {
      setIsLoading(false);
      setIsSubmitting(false);
    }
  };


  return (
    <div className={`popup-user ${isOpen ? 'open' : ''}`}>
      <form ref={popupRef} onSubmit={handleSubmit}>
        <h2 className="title">Add New User</h2>
        <p className="paragraph-green">
          Fill in the required details to add a new user.
        </p>
        <div className='form-step'>
          <TextInput
            name="username" id="username" placeholder="Username" required
            value={username}
            setValue={setUsername}
          />
          <TextInput
            name="email" id="email" placeholder="Email" required
            value={email}
            setValue={setEmail}
          />
          <div className='names'>
            <TextInput
              name="first_name" id="first_name" placeholder="First Name" required
              value={firstName}
              setValue={setFirstName}
            />
            <TextInput
              name="last_name" id="last_name" placeholder="Last Name" required
              value={lastName}
              setValue={setLastName}
            />
          </div>

          {/* <DataListInput
  id="permission" name="permission" placeholder="Select a permission" required
  options={permissions}
  value={permission}
  setValue={setPermission}
/> */}

          <div className='names'>
            <DataListInput
              id="position"
              name="position"
              placeholder="Select Role"
              options={positions}
              value={selectedPosition}
              setValue={setSelectedPosition}
            />

            <DataListInput
              id="hospital" name="hospital" placeholder="Select Hospital" required
              options={hospitals}
              value={hospital}
              setValue={setHospital}
            />
          </div>


          {
            permission &&
            <ViewPositionDetailsPopup page="user" onClose={() => setPermission(null)} permissions={permission} />
          }

          <div className="popup-buttons">
            <PrimaryButton type="submit" isLoading={isSubmitting} buttonText="Save" processingText={'Saving New User'}>Submit</PrimaryButton>
            <SecondaryButton onClick={onClose} isLoading={false} buttonText="Cancel">Cancel</SecondaryButton>

          </div>
        </div>
      </form>



    </div>
  );
};

export default AddUserForm;
