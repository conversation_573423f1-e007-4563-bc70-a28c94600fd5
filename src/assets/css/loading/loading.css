/* Login Loading Styles */
.login-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.login-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  text-align: center;
}

.login-loading-content .logo {
  max-width: 100px;
  margin: 0 auto;
}

.login-loading-content p {
  font-size: 16px !important;
  color: #333;
  font-weight: 500;
}

/* Table Loading Styles */
.table-loading-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(255, 255, 255, 0.8) !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 100 !important;
  border-radius: 8px !important;
  pointer-events: none !important; /* Allow clicks to pass through */
}

.table-container {
  position: relative !important; /* Ensure the container has position relative */
}

.table-loading-overlay p {
  margin-top: 10px;
  font-size: 14px !important;
  color: #333;
}

/* Override any global loading overlay */
.loading-overlay {
  display: none !important;
}

/* Ensure loading is confined to tables only */
.measures-page .measures,
.targets,
.hospital-content,
.position-page,
.tab-contents-hospitals,
.table-container {
  position: relative !important;
}

/* Page Loading Styles */
.page-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.page-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.page-loading-content p {
  font-size: 16px !important;
  color: #333;
  margin-top: 10px;
}

/* Shared Spinner Style */
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
