@import '../../variables';

input {
    background-color: transparent;

    &:focus {
        background-color: transparent !important;
    }
   
}

.css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
    color: gray;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    padding: 14px;
    border: 1px solid #fefefe;
}
.field {
    display: grid;
    height: fit-content;
    gap: 0.2rem;
    position: relative;

    .input {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border: 1px solid #eee;
        padding: 0.8rem;
        color: gray;
        border-radius: .4rem;

        transition: all 0.3s ease;


    }

    .active {
        border-color: $primary-color;
    }

    input {
        width: 100%;
        border: none;
        font-size: inherit;
        outline: none;


        &:focus {
            outline: none;
        }

        &::placeholder {
            color: gray;
        }
    }

    .date-input {
        border: 0;
        padding: 0;
        width: 100% !important;
        

        input {
            border: 0;
            padding: 0.8rem !important;
            width: 100% !important;
            
        }
    }

    .options {
        position: absolute;
        top: 4.6rem;
        left: 0;
        right: 0;
        z-index: 2;
        background-color: white;
        padding: 1rem;
        border-radius: 0.8rem;
        border: 1px solid #eee;
        scroll-behavior: auto;
        cursor: pointer;
        width: fit-content;

        .option {
            padding: 0.8rem;
            border-bottom: 1px solid #eee;

            &:last-child {
                border-bottom: none;
            }
        }
    }
}

form {
    display: grid;
    gap: 1rem;
    height: fit-content;
    background-color: white;
    padding: 20px;
    border-radius: 8px;

}

.names{
    display: flex;
    gap:24px;
}

.form {
    display: grid;
    gap: 1rem;
    height: fit-content;
    width: 100%;

      .half{
         display: flex;
         flex-direction: row !important;
          gap: 1rem;
        }
}
.no-data {
    padding: 4px;
    color: red;
    text-align: center;
    font-size: 14px;
}
.option {
    padding: 4px;
    cursor: pointer;
}
.option:hover {
    background-color: #f0f0f0;
}

.form-link {
    color: $primary-color;
    text-decoration: underline;
}

.buttons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.MuiFormControl-root.MuiTextField-root.css-z3c6am-MuiFormControl-root-MuiTextField-root {
    width: 100% !important;
}