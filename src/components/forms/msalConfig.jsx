import { PublicClientApplication } from '@azure/msal-browser';

export const msalConfig = {
    auth: {
        clientId: '7c88fb74-5f1a-463f-9cd9-1914236090f8',
        authority: 'https://login.microsoftonline.com/28ee6dbe-d6ae-40b3-88ed-f1c50e995ea0/',
        redirectUri: 'http://localhost:3000/',
    },
    cache: {
        cacheLocation: 'localStorage',
        storeAuthStateInCookie: false,
    },
};


export const loginRequest = {
    scopes: ['User.Read'], 
};

// carngeie

//pawshla

// this was for testing to the hospitals ms authentication
// export const hospitalDomains = {
//     'localhost': {
//         clientId: '7c88fb74-5f1a-463f-9cd9-1914236090f8',
//         authority: 'https://login.microsoftonline.com/28ee6dbe-d6ae-40b3-88ed-f1c50e995ea0/',
//     },
//     'hospital-a.com': {
//         clientId: '7c88fb74-5f1a-463f-9cd9-1914236090f8',
//         authority: 'https://login.microsoftonline.com/28ee6dbe-d6ae-40b3-88ed-f1c50e995ea0/',
//     },
//     'hospital-b.com': {
//         clientId: '8a99ff75-6f2a-573g-1ce2-2925337001f9',
//         authority: 'https://login.microsoftonline.com/39ff7dbe-d7af-41c4-99ed-g2d50e006fb1/',
//     },
//     'hospital-c.com': {
//         clientId: '9b00gg76-7f3b-684h-2df3-3936448012g0',
//         authority: 'https://login.microsoftonline.com/4aff8ebe-e8bg-52d5-aaee-h3e60f007gc2/',
//     },
//     'hospital-d.com': {
//         clientId: 'abc12345-def6-7890-ghij-klmnopqrstuv',
//         authority: 'https://login.microsoftonline.com/5bgg9fbf-f9ch-63e6-bbee-i4f71g008hd3/',
//     },
//     'hospital-e.com': {
//         clientId: 'def23456-ghi7-8901-jklm-nopqrstuvwxz',
//         authority: 'https://login.microsoftonline.com/6cgh0gcg-gadi-74f7-ccff-j5g82h009ie4/',
//     },
// };

// const domain = window.location.hostname;
// const hospitalConfig = hospitalDomains[domain];

// if (!hospitalConfig) {
//     throw new Error(`No configuration found for domain: ${domain}`);
// }

// export const msalInstance = new PublicClientApplication({
//     auth: {
//         clientId: hospitalConfig.clientId,
//         authority: hospitalConfig.authority,
//         redirectUri: 'http://localhost:3000/',
//     },
//     cache: {
//         cacheLocation: 'localStorage',
//         storeAuthStateInCookie: false,
//     },
// });






