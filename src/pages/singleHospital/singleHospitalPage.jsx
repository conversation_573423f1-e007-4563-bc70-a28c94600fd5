import React, { useEffect, useState } from "react";
import api, { API_URL } from "../../api";
import SingleHospitalCard from "../../components/singleHospital/singleHospitalCard";
import SinglePageBarChart from "../../components/singleHospital/barChart";
import SinglePagePieChart from "../../components/singleHospital/PieChart";
import SinglePageTable from "../../components/singleHospital/table";
import { useNavigate } from "react-router-dom";
import EditHospitalPopup from "../../components/singleHospital/editHospitalPopup";
import "../../assets/css/dashboard/dashboard.css";
import { FileExportIcon, ArrowDown01Icon } from "hugeicons-react";
import "../../assets/css/singleHospitalPage/singleHospital.css";
import DashboardContainer from "../../components/dashboard/DashboardContainer";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { useParams } from "react-router-dom";
import jsPDF from 'jspdf';
import TableLoading from "../../components/loading/TableLoading";
import autoTable from "jspdf-autotable";


const monthToNumber = {
  January: 1, February: 2, March: 3, April: 4, May: 5, June: 6,
  July: 7, August: 8, September: 9, October: 10, November: 11, December: 12,
  YTD: 'ytd'
};

function reorganizeCardData(data) {
  const cardData = [
      { name: 'Acute', value: data['total_acute'] },
      { name: 'Swing Bed', value: data['total_swing_bed'] },
      { name: 'Observation', value: data['total_observation'] },
      { name: 'Emergency Room', value: data['total_emergency_room'] }
  ];

  return cardData;
}

function transformingLegendPieChartData(pieData) {
  const legendPieData = { ...pieData };
  let total_falls = legendPieData["Falls without Injury"] + legendPieData["Falls with Minor Injury"] + legendPieData["Falls with Major Injury"];

  let fallsWithoutInjury = legendPieData["Falls without Injury"];
  let fallsWithMinorInjury = legendPieData["Falls with Minor Injury"];
  let fallsWithMajorInjury = legendPieData["Falls with Major Injury"];
  let falls_without_injury_percentage = !isNaN(fallsWithoutInjury) && total_falls !== 0? Math.round((fallsWithoutInjury * 100) / total_falls): 0;
  let falls_with_minor_injury_percentage = !isNaN(fallsWithoutInjury) && total_falls !== 0? Math.round((fallsWithMinorInjury * 100) / total_falls): 0;
  let falls_with_major_injury_percentage = !isNaN(fallsWithoutInjury) && total_falls !== 0? Math.round((fallsWithMajorInjury * 100) / total_falls): 0;

  legendPieData["Falls without Injury"] = falls_without_injury_percentage;
  legendPieData["Falls with Minor Injury"] = falls_with_minor_injury_percentage;
  legendPieData["Falls with Major Injury"] = falls_with_major_injury_percentage;

  const dataArray = Object.entries(legendPieData).map(([label, value]) => ({
      label,
      value,
  }));

  return dataArray;
}
// Removed old LoadingOverlay in favor of TableLoading component

function SingleHospitalPageContent({hospitalId}) {

  const [hospitalCardTotals, setHospitalCardTotals] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showEditPopup, setShowEditPopup] = useState(false);
  const [hospitalName, setHospitalName] = useState('');
  const [PieChartData, setPieChartData] = useState([]);
  const [legendPieChartData, setLegendPieChartData] = useState([]);
  const [measureTableData, setMeasureTableData] = useState('');
  const [singleBarChartData, setSingleBarChartData] = useState('');
  const [currentDateYear, setCurrentDateYear] = useState(new Date().getFullYear());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState("YTD");
  const [isOverlayVisible, setIsOverlayVisible] = useState(false);
  const [isLoadingYTD, setIsLoadingYTD] = useState(false);


  const [filterDurationYear, setFilterDurationYear] = useState(
    new Date().getFullYear()
  );

  const [prevYear, setPrevYear] = useState(new Date().getFullYear() - 1);
  const [prevYearMinusTwo, setPrevYearMinusTwo] = useState(
    new Date().getFullYear() - 2
  );
  const [prevYearMinusThree, setPrevYearMinusThree] = useState(
    new Date().getFullYear() - 3
  );
  const navigate = useNavigate();
  const { id } = useParams();

  const options = ["This Year", "Last Year"];

  const handleDurationYearChange = (value) => {
    setFilterDurationYear(value);
    setSelectedYear(value)
  };

  const handleMonthChange = (value) => {
    setSelectedMonth(value);
  };

  useEffect(() => {
    const fetchHospitalDetails = async () => {
        try {
          setIsOverlayVisible(true);

          if (selectedMonth === 'YTD') {
            setIsLoadingYTD(true);
            const currentYear = new Date().getFullYear();
            const currentMonth = new Date().getMonth() + 1;
            const endMonth = (Number(selectedYear) === currentYear) ? currentMonth : 12;

            // Initialize aggregated data structures
            let aggregatedTotals = {
              total_acute: 0,
              total_swing_bed: 0,
              total_observation: 0,
              total_emergency_room: 0
            };

            let aggregatedChartData = {};
            let aggregatedPieData = {};

            // For YTD, we'll use the current month's measure table data (no aggregation)
            let currentMeasureData = {};

            // Fetch data for each month and aggregate
            for (let month = 1; month <= endMonth; month++) {
              try {
                const response = await api.get(`${API_URL}/hospitals/hospital/${hospitalId}/?year=${selectedYear}&month=${month}`);

                // Aggregate totals
                const monthTotals = response.data["totals"];
                if (monthTotals) {
                  aggregatedTotals.total_acute += monthTotals.total_acute || 0;
                  aggregatedTotals.total_swing_bed += monthTotals.total_swing_bed || 0;
                  aggregatedTotals.total_observation += monthTotals.total_observation || 0;
                  aggregatedTotals.total_emergency_room += monthTotals.total_emergency_room || 0;
                }

                // Keep the most recent month's measure table data (no aggregation for performance measures)
                const monthMeasureData = response.data["measure_table_data"][selectedYear];
                if (monthMeasureData && month === endMonth) {
                  currentMeasureData = monthMeasureData;
                }

                // Aggregate chart data
                const monthChartData = response.data["chart_data"][selectedYear];
                if (monthChartData) {
                  for (const [measure, monthData] of Object.entries(monthChartData)) {
                    if (!aggregatedChartData[measure]) {
                      aggregatedChartData[measure] = {};
                    }
                    for (const [monthName, value] of Object.entries(monthData)) {
                      if (!aggregatedChartData[measure][monthName]) {
                        aggregatedChartData[measure][monthName] = 0;
                      }
                      aggregatedChartData[measure][monthName] += value || 0;
                    }
                  }
                }

                // Aggregate pie data
                const monthPieData = response.data["falls_injury_type"];
                if (monthPieData) {
                  for (const [key, value] of Object.entries(monthPieData)) {
                    if (!aggregatedPieData[key]) {
                      aggregatedPieData[key] = 0;
                    }
                    aggregatedPieData[key] += value || 0;
                  }
                }

              } catch (monthError) {
                // Continue with other months if one fails
              }
            }

            // Set aggregated data
            setHospitalCardTotals(reorganizeCardData(aggregatedTotals));
            setMeasureTableData(currentMeasureData); // Use current month's data, not aggregated
            setSingleBarChartData(aggregatedChartData);
            setPieChartData(aggregatedPieData);
            const legendPieDataArray = transformingLegendPieChartData(aggregatedPieData);
            setLegendPieChartData(legendPieDataArray);

            setIsLoadingYTD(false);
          } else {
            // Regular month selection
            const monthParam = monthToNumber[selectedMonth] || 0;
            const response = await api.get(`${API_URL}/hospitals/hospital/${hospitalId}/?year=${selectedYear}&month=${monthParam}`);

            const measuresTableData = response.data["measure_table_data"][selectedYear];
            setMeasureTableData(measuresTableData);

            const barChartData = response.data["chart_data"][selectedYear];
            setSingleBarChartData(barChartData);

            const cardsTotals = response.data["totals"];
            setHospitalCardTotals(reorganizeCardData(cardsTotals));

            const pieData = response.data["falls_injury_type"];
            setPieChartData(pieData);
            const legendPieDataArray = transformingLegendPieChartData(pieData);
            setLegendPieChartData(legendPieDataArray);
          }

          setIsOverlayVisible(false);
        } catch (error) {
          setIsOverlayVisible(false);
          setIsLoadingYTD(false);
        }
    }

    fetchHospitalDetails()

    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  }, [hospitalId, selectedYear, selectedMonth]);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  }

  const handleShowPopup = () => {
    setShowEditPopup(!showEditPopup);
  }

  const handleExportPDF = () => {
    if (!measureTableData || typeof measureTableData !== 'object') {
      console.error("No valid data available for export.");
      alert("No data to export!");
      return;
    }

    const doc = new jsPDF();
    const margin = 5;
    const title = "Hospital Performance Measures Report";
    let startY = margin + 5;

    // Title
    doc.setFontSize(14);
    doc.text(title, doc.internal.pageSize.getWidth() / 2, margin, { align: "center" });
    startY += 8;
    const headers = ["Measure Name", "Goal", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    const rows = [];

    Object.keys(measureTableData).forEach((measureDataKey) => {
      const measure = measureTableData[measureDataKey];
      const row = [
        measureDataKey,
        measure.BM !== null ? measure.BM : "-",
      ];


      for (let i = 1; i <= 12; i++) {
        const value = measure["months"]?.[i] ?? "-";
        row.push(measure.measure_unit === "Percentage" && value !== "-" ? `${value}%` : value);
      }

      rows.push(row);
    });


    autoTable(doc, {
      startY,
      head: [headers],
      body: rows,
      theme: "striped",
      margin: { left: margin, right: margin },
      styles: { fontSize: 9 },
      headStyles: { fillColor: [41, 128, 185], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [240, 240, 240] },
      didDrawPage: (data) => {

        doc.setFontSize(8);
        doc.text(
          `Page ${doc.internal.getNumberOfPages()}`,
          doc.internal.pageSize.getWidth() / 2,
          doc.internal.pageSize.getHeight() - 10,
          { align: "center" }
        );
      },
    });


    doc.save(`hospital_measure_data_${selectedYear}.pdf`);
  };



  const handleSelectedMonth = (value) => {
    setSelectedMonth(value);
  };

  return isLoading ? (
    "Getting data.."
  ) : (
    <div className="single-hospitals-page">
      <div className="sidebar"></div>
      <div className="main-container">
        <div className="navbar"></div>
        <div className="hospitals-container">
          <div className="top-actions">
          <div className="export-icon export-bttn" onClick={handleExportPDF}>
      <FileExportIcon size={24} />
      <p>Export to PDF</p>
    </div>

            <div className="hospital-year">
            <Select
          onChange={(e) => {
            handleDurationYearChange(e.target.value);
          }}
          name="duration"
          id="duration"
          value={selectedYear}
        >
          <MenuItem value={currentDateYear}>{currentDateYear}</MenuItem>
          <MenuItem value={prevYear}>{prevYear}</MenuItem>
          <MenuItem value={prevYearMinusTwo}>{prevYearMinusTwo}</MenuItem>
           <MenuItem value={prevYearMinusThree}>{prevYearMinusThree}</MenuItem>
        </Select>
        <Select
          onChange={(e) => {
            handleMonthChange(e.target.value);
          }}
          name="selectedMonth"
          id="selectedMonth"
          value={selectedMonth}
        >
          <MenuItem value="YTD">YTD (Year-to-Date)</MenuItem>
          <MenuItem value="January">January</MenuItem>
          <MenuItem value="February">February</MenuItem>
          <MenuItem value="March">March</MenuItem>
          <MenuItem value="April">April</MenuItem>
          <MenuItem value="May">May</MenuItem>
          <MenuItem value="June">June</MenuItem>
          <MenuItem value="July">July</MenuItem>
          <MenuItem value="August">August</MenuItem>
          <MenuItem value="September">September</MenuItem>
          <MenuItem value="October">October</MenuItem>
          <MenuItem value="November">November</MenuItem>
          <MenuItem value="December">December</MenuItem>
        </Select>
            </div>
            {/* <SecondaryButton
              buttonText="Edit Details"
              isLoading={isSubmitting}
              onClick={handleShowPopup}
              processingText={"Saving"}
              iconClass={<PencilEdit02Icon />}
            /> */}
          </div>

          {selectedMonth === 'YTD' && (
            <div className="ytd-indicator">
              {isLoadingYTD ? (
                <>⏳ Loading Year-to-Date data...</>
              ) : (
                <>
                  📊 Displaying Year-to-Date data (January - {
                    Number(selectedYear) === new Date().getFullYear()
                      ? new Date().toLocaleString('default', { month: 'long' })
                      : 'December'
                  })
                </>
              )}
            </div>
          )}

          {hospitalCardTotals && hospitalCardTotals.length > 0 ? (
            <div className="total-hospital-cards">
              {hospitalCardTotals.map((hospital, index) => (
                <SingleHospitalCard
                  className="total-card"
                  key={index}
                  id={hospital.id}
                  name={hospital.name}
                  value={hospital.value}
                  icon={hospital.icon}
                />
              ))}
            </div>
          ) : (
            "No data available"
          )}

          <div className="charts">
            <div className="bar-chart-container table-container">
              {isOverlayVisible && <TableLoading />}
              <SinglePageBarChart singleBarChartData={singleBarChartData} hospitalId={hospitalId} defaultMeasure />
            </div>
            <div className="pie-chart-container table-container">
              {isOverlayVisible && <TableLoading />}
              <SinglePagePieChart PieChartData={PieChartData} legendPieChartData={legendPieChartData}/>
            </div>
          </div>

          <div className="table-container">
            <h3>Performance Measures</h3>
            {isOverlayVisible && <TableLoading />}
            <SinglePageTable measuresTableData={measureTableData}/>
          </div>

          {showEditPopup && (
            <div className="overlay">
              <EditHospitalPopup />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

const SingleHospitalPage = () => {
  const { hospitalName } = useParams();
  const { hospitalId } = useParams();
  return (
    <DashboardContainer
      content={<SingleHospitalPageContent hospitalId={hospitalId}/>}
      pageTitle={hospitalName}
    />
  );
};

export default SingleHospitalPage;
