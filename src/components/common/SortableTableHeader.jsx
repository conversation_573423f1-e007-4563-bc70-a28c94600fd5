import React from 'react';
import { ArrowUp01Icon, ArrowDown01Icon } from 'hugeicons-react';
import '../../assets/css/components/common/sortable-table-header.css';

const SortableTableHeader = ({ 
    label, 
    sortKey, 
    currentSort, 
    onSort, 
    className = '',
    ...props 
}) => {
    const handleSort = () => {
        if (currentSort.key === sortKey) {
            // If already sorting by this column, toggle direction
            const newDirection = currentSort.direction === 'asc' ? 'desc' : 'asc';
            onSort({ key: sortKey, direction: newDirection });
        } else {
            // If sorting by different column, start with ascending
            onSort({ key: sortKey, direction: 'asc' });
        }
    };

    const getSortIcon = () => {
        if (currentSort.key !== sortKey) {
            return (
                <div className="sort-icons">
                    <ArrowUp01Icon size={12} className="sort-icon inactive" />
                    <ArrowDown01Icon size={12} className="sort-icon inactive" />
                </div>
            );
        }

        return (
            <div className="sort-icons">
                <ArrowUp01Icon 
                    size={12} 
                    className={`sort-icon ${currentSort.direction === 'asc' ? 'active' : 'inactive'}`} 
                />
                <ArrowDown01Icon 
                    size={12} 
                    className={`sort-icon ${currentSort.direction === 'desc' ? 'active' : 'inactive'}`} 
                />
            </div>
        );
    };

    return (
        <th 
            className={`sortable-header ${className}`} 
            onClick={handleSort}
            {...props}
        >
            <div className="header-content">
                <span className="header-label">{label}</span>
                {getSortIcon()}
            </div>
        </th>
    );
};

export default SortableTableHeader;
