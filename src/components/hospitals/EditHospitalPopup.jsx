import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import Cookies from 'js-cookie';
import { API_URL } from '../../api';
import { PrimaryButton, SecondaryButton } from "../../components/forms/buttons";
import { TextInput } from '../forms/Input';

const EditHospitalPopup = ({ hospital, onClose, onUpdateHospital, fetchHospitals }) => {
  const [formData, setFormData] = useState({
    name: hospital?.name || '',
    address: hospital?.address || '',
    phonenumber: hospital?.phonenumber || '',
    cnn: hospital?.cnn || '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const popupRef = useRef(null);


  const handleClickOutside = (event) => {
    if (popupRef.current && !popupRef.current.contains(event.target)) {
      onClose();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleEditHospital = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const token = Cookies.get('accessToken');
    try {
      const response = await axios.put(
        `${API_URL}/hospitals/update_hospital/${hospital.id}/`,
        formData,

        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );



      if (response.status === 200) {
        setSuccessMessage('Hospital updated successfully!');
        await fetchHospitals();
        onClose();
      }
    } catch (error) {
      setError('Error updating hospital.');
      console.error('Error updating hospital:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="popup popup-overlay">
      <div className="add-target-popup" ref={popupRef}>
        <h3>Edit Hospital</h3>
        {error && <div className="error-message">{error}</div>}
        {successMessage && <div className="success-message">{successMessage}</div>}
        <form onSubmit={handleEditHospital}>

          <div className='form-group'>

            <label htmlFor="name">Hospital Name</label>
            <TextInput
              id="name"
              name="name"
              placeholder="Hospital Name"
              value={formData.name}
              setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, name: value }))}
            />
          </div>
          <div className='form-group'>
            <label htmlFor="address">Address</label>

            <TextInput
              id="address"
              name="address"
              placeholder="Address"
              value={formData.address}
              setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, address: value }))}
            />
          </div>
          <div className='form-group'>
            <label htmlFor="phonenumber">Phone Number</label>
            <TextInput
              id="phonenumber"
              name="phonenumber"
              placeholder="Phone Number"
              value={formData.phonenumber}
              setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, phonenumber: value }))}
            />
          </div>
          <div className='form-group'>
            <label htmlFor="cnn">CMS Certification Number (CCN)</label>

            <TextInput
              id="cnn"
              name="cnn"
              placeholder="CMS Certification Number (CCN)"
              value={formData.cnn}
              setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, cnn: value }))}
            />
          </div>


          <div className="action-btn">
            <PrimaryButton isLoading={isSubmitting} buttonText="Update Hospital" type="submit" />
            <SecondaryButton onClick={onClose} buttonText="Cancel" />
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditHospitalPopup;
