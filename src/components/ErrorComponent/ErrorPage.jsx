import React from "react";
import { Link } from "react-router-dom";
import useNetworkStatus from "./useNetworkStatus";
import { AlertDiamondIcon, ConnectIcon, UsbErrorIcon, AuthorizedIcon, Exchange02Icon } from "hugeicons-react";
import '../../assets/css/errors/error.css';


const ErrorPage = ({ errorCode, errorMessage }) => {
  const link = Link;
  const isOnline = useNetworkStatus();

  const handleError = (errorCode) => {
    if (!isOnline) {
      return;
    }
    switch (errorCode) {
      case 403:
        Link("/403");
        break;
      case 404:
        Link("/404");
        break;
      case 500:
        Link("/500");
        break;
      default:
        Link("/404");
    }
  };

  const errorDetails = {
    403: {
      icon: <AuthorizedIcon size={64} color={"#07AEEF"} variant={"stroke"} />,
      title: "You're not permitted to access this",
      description: "The page you're trying to access has restricted access. If you feel this is a mistake, contact your admin.",
    },
    404: {
      icon: <AlertDiamondIcon size={64} color={"#07AEEF"} variant={"stroke"} />,
      title: "Ooups,page not found",
      description:
       "We are every sorry for the inconvenience.It looks like you’re trying to access a page that been deleted or never even existed.",
    },
    500: {
      icon: <UsbErrorIcon size={64} color={"#07AEEF"} variant={"stroke"} />,
      title: "Internal Server Error",
      description: "Something went wrong on our end. Please try again later.",
    },
    409: {
      icon: <Exchange02Icon size={64} color={"#07AEEF"} variant={"stroke"} />,
      title: "Conflict Detected",
      description: "There is a data conflict. Please review your input and try again.",
    },
    offline: {
      icon: <ConnectIcon size={64} color={"#07AEEF"} variant={"stroke"} />,
      title: "No Internet Connection",
      description: "It seems you're offline. Check your connection and try again.",
    },
  };
  const { icon, title, description } = isOnline
    ? errorDetails[errorCode] || {
      title: "Error Occurred",
      description: errorMessage || "An unexpected error occurred.",
    }
    : errorDetails["offline"];

  return (
    <div className="error-pages">
      <img src={"/logo512.png"} alt="logo" />
      <div className="page-content">
        {icon}
        <h1>{title}</h1>
        <p>{description}</p>
      </div>

    </div>
  );
};

export default ErrorPage;
