.dashboard {
  display: flex;
  align-items: start;
}
.dashboard .sidebar-logout {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #07AEEF;
  height: 100vh;
  color: white;
}
.dashboard .sidebar-logout .side-bar {
  min-width: 252px;
  position: sticky;
}
.dashboard .sidebar-logout .side-bar .close-menu {
  display: none;
  position: absolute;
  top: 1rem;
  right: 1rem;
  cursor: pointer;
  z-index: 999;
  color: white;
}
.dashboard .sidebar-logout .side-bar .branding {
  display: flex;
  align-items: center;
  font-size: 1rem;
  height: 4rem;
  padding: 1rem;
  gap: 0.5rem;
}
.dashboard .sidebar-logout .side-bar .menu-items {
  display: grid;
  height: -moz-fit-content;
  height: fit-content;
}
.dashboard .sidebar-logout .side-bar .menu-items .menu-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.8rem;
}
.dashboard .sidebar-logout .side-bar .menu-items .menu-item:hover {
  background-color: white;
  color: #07AEEF;
}
.dashboard .sidebar-logout .side-bar .menu-items .menu-item:hover .initials {
  border-color: #07AEEF;
}
.dashboard .sidebar-logout .side-bar .active {
  background-color: white;
  color: #07AEEF;
}
.dashboard .sidebar-logout .profile-item {
  display: flex;
  gap: 12px;
  padding: 24px;
  cursor: pointer;
}
.dashboard .dashboard-content {
  flex: 1;
  width: calc(100vw - sidebar-width);
  background-color: whitesmoke;
  overflow: auto;
}
.dashboard .dashboard-content .dashboard-header {
  height: 6rem;
  width: 100%;
  background-color: white;
  padding: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: sticky;
  top: 0;
  z-index: 2;
}
.dashboard .dashboard-content .dashboard-header .openMenu {
  display: none;
}
.dashboard .dashboard-content .dashboard-header .breadcrumbs {
  display: grid;
  height: -moz-fit-content;
  height: fit-content;
  gap: 0.5rem;
}
.dashboard .dashboard-content .dashboard-header .breadcrumbs .menu-title {
  display: flex;
  gap: 10px;
  align-items: center;
}
.dashboard .dashboard-content .dashboard-header .breadcrumbs .breadcrumb-list {
  display: flex;
  align-items: center;
  gap: 0.4rem;
}
.dashboard .dashboard-content .dashboard-header .breadcrumbs .breadcrumb-list .breadcrumb {
  display: flex;
  text-transform: capitalize;
  align-items: center;
  gap: 0.4rem;
  color: gray;
}
.dashboard .dashboard-content .dashboard-header .breadcrumbs .breadcrumb-list .breadcrumb:last-child {
  color: #07AEEF;
}
.dashboard .dashboard-content .dashboard-header .header-content {
  flex: 1;
  padding: 0.6rem 1rem;
  border-radius: 0.2rem;
  border: 1px solid #E5E5E5;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .profile img {
  height: 2.8rem;
  width: 2.8rem;
  padding: 0.2rem;
  border-radius: 50%;
  border: 1px solid #E5E5E5;
  background-color: white;
  -o-object-fit: contain;
     object-fit: contain;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .profile-overlay {
  background-color: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .profile-drop-down {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: white;
  position: absolute;
  top: 80px;
  right: 10px;
  z-index: 2;
  padding: 40px;
  border-radius: 5px;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .profile-drop-down .profile-name {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .profile-drop-down .profile-name .name-position {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .profile-drop-down .profile-name img {
  height: 2.8rem;
  width: 2.8rem;
  border-radius: 50%;
  border: 1px solid #E5E5E5;
  background-color: #E5E5E5;
  -o-object-fit: cover;
     object-fit: cover;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .profile-drop-down .profile-menu {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .profile-drop-down .profile-menu .profile-item {
  cursor: pointer;
  display: flex;
  gap: 10px;
  align-items: center;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .notification {
  position: relative;
  display: inline-block;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .notification .dot {
  position: absolute;
  top: -5px;
  right: -5px;
  height: 10px;
  width: 10px;
  background-color: red;
  border-radius: 50%;
}
.dashboard .dashboard-content .dashboard-header .header-content .profile-and-links .notification .unread-count {
  position: absolute;
  top: -10px;
  right: -15px;
  background-color: red;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 12px;
}
.dashboard .dashboard-content .content {
  padding: 1rem;
  height: calc(100vh - 7rem);
}

.drop-down {
  max-height: 3.5rem;
  overflow: hidden;
  transition: all 0.5s linear;
  max-height: 100vh;
}
.drop-down .drop-down-icon {
  transform: rotate(0);
  transition: all 0.5s linear;
}
.drop-down .drop-down-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 1rem;
}
.drop-down .drop-down-title .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  gap: 1rem;
  cursor: pointer;
}
.drop-down .drop-down-content {
  background-color: rgba(255, 255, 255, 0.138);
  margin: 1rem;
  border-radius: 8px;
}
.drop-down .drop-down-content .menu-item {
  display: none;
}

.initials {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 1.5rem;
  width: 1.5rem;
  border: 1px solid white;
  border-radius: 0.2rem;
}

.sidebar-logout.hidden {
  display: none;
}

@media screen and (max-width: 1080px) {
  .dashboard .sidebar-logout .side-bar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    z-index: 2000;
    width: 30%;
    height: 100vh;
    position: absolute;
    background-color: #07AEEF;
  }
  .dashboard .sidebar-logout .side-bar .close-menu {
    display: block;
  }
  .dashboard .sidebar-logout.show .side-bar {
    transform: translateX(0);
  }
  .dashboard .sidebar-logout.hide .side-bar {
    transform: translateX(-100%);
  }
  .dashboard .dashboard-content {
    width: 100%;
  }
  .dashboard .dashboard-content .dashboard-header .openMenu {
    display: block;
  }
}
@media screen and (max-width: 560px) {
  .dashboard .side-bar {
    max-width: unset;
    width: 50vw;
  }
  .dashboard .dashboard-content .dashboard-header .breadcrumbs .breadcrumb-list {
    display: none;
  }
}/*# sourceMappingURL=dashboardContainer.css.map */