import React, { useEffect, useState } from 'react'
import api, { API_URL } from '../../api'
import BarGraph from '../../components/hospitals/HositalBarGraph'
import "../../assets/css/hospitals/hospitals.css"
import DashboardTotalCard from "../../components/dashboard/DashboardTotalCard";
import DashboardContainer from '../../components/dashboard/DashboardContainer'
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import {Timer02Icon,BedIcon,SearchingIcon,WorkoutRunIcon,PlusSignIcon,} from "hugeicons-react";
import { PrimaryButton } from '../../components/forms/buttons'
import AddHospitalPopup from '../../components/hospitals Popup/AddHospitalPopup'
import Hospitals from './Hospitals'
import { hasRole } from '../../services/userPosition'
import TableLoading from '../../components/loading/TableLoading'

const monthToNumber = {
  January: 1, February: 2, March: 3, April: 4, May: 5, June: 6,
  July: 7, August: 8, September: 9, October: 10, November: 11, December: 12,
  YTD: 'ytd'
};

// Removed old LoadingOverlay in favor of TableLoading component
const HospitalsPageContent = () => {
    const [hospitals, setHospitals] = useState([])
    const [isLoading, setIsLoading] = useState(true)
    const [totalData, setTotalData] = useState({});
    const [currentDateYear, setCurrentDateYear] = useState(
      new Date().getFullYear()
    );
    const [prevYear, setPrevYear] = useState(new Date().getFullYear() - 1);
  const [prevYearMinusTwo, setPrevYearMinusTwo] = useState(
    new Date().getFullYear() - 2
  );
  const [prevYearMinusThree, setPrevYearMinusThree] = useState(
    new Date().getFullYear() - 3
  );
  const [barData, setBarData] = useState(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState("YTD");
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('analytics');
  const [isOverlayVisible, setIsOverlayVisible] = useState(false);
  const [isLoadingYTD, setIsLoadingYTD] = useState(false);

  const fetchTotalsData = async (value) => {
      try {
        if (selectedMonth === 'YTD') {
          setIsLoadingYTD(true);
          const currentYear = new Date().getFullYear();
          const currentMonth = new Date().getMonth() + 1;
          const endMonth = (Number(value) === currentYear) ? currentMonth : 12;

          let aggregatedData = {
            total_acute: 0,
            total_swing_bed: 0,
            total_observation: 0,
            total_emergency_room: 0
          };

          for (let month = 1; month <= endMonth; month++) {
            try {
              const response = await api.get(`${API_URL}/dashboard/dashboard_totals/${value}/${month}/`);
              if (response.status === 200 && response.data[Number(value)]) {
                const monthData = response.data[Number(value)];
                aggregatedData.total_acute += monthData.total_acute || 0;
                aggregatedData.total_swing_bed += monthData.total_swing_bed || 0;
                aggregatedData.total_observation += monthData.total_observation || 0;
                aggregatedData.total_emergency_room += monthData.total_emergency_room || 0;
              }
            } catch (monthError) {
              // Continue with other months if one fails
            }
          }
          setTotalData(aggregatedData);
          setIsLoadingYTD(false);
        } else {
          const monthParam = monthToNumber[selectedMonth] || 0;
          const response = await api.get(`${API_URL}/dashboard/dashboard_totals/${selectedYear}/${monthParam}/`);
          if (response.status === 200) {
            setTotalData(response.data[Number(value)]);
          }
        }
        setIsOverlayVisible(false);
      } catch (error) {
        setIsOverlayVisible(false);
        setIsLoadingYTD(false);
      }
    };

  const handleAddHospital = async (value)=>{
    setIsPopupOpen(!isPopupOpen);
  }

    const handlePopupSubmit = (e) => {
      e.preventDefault();
      handleClosePopup();
    };

    const handleClosePopup = () => {
      setIsPopupOpen(false);
    };


    useEffect(() => {
      const fetchData = async () => {
        try {
          setIsOverlayVisible(true);
          const monthParam = selectedMonth === 'YTD' ? 0 : (monthToNumber[selectedMonth] || 0);
          const response = await api.get(
            `${API_URL}/hospitals/data/`,
            {
              params: {
                "year": selectedYear || 2024,
                "month": monthParam
              }
            }
          );

          const result = response.data;
          setBarData(result);
          setIsLoading(false)
        } catch (error) {
          setIsOverlayVisible(false);
        }
      };

      fetchData();
      fetchTotalsData(selectedYear);
    }, [selectedYear, selectedMonth]);


    const handleDurationYearChange = (value) => {
      setSelectedYear(value)
    };

    const handleMonthChange = (value) => {
      setSelectedMonth(value);
    };

    if (!barData) {
      return <p>Loading...</p>;
    }

    return isLoading ? 'Getting data..' : (

      <div className='hospitals-page'>
        <div className="header"></div>
        <div className="tabs">
          <div
            className={`tab ${activeTab === 'analytics' ? 'active' : ''}`}
            onClick={() => setActiveTab('analytics')}
          >
            Hospitals Analytics
          </div>
          <div
            className={`tab ${activeTab === 'allHospitals' ? 'active' : ''}`}
            onClick={() => setActiveTab('allHospitals')}
          >
          All Hospitals
          </div>
        </div>

<div className='tab-contents'>
{activeTab === 'analytics' && (
          <div className='select-button'>
            <Select
              onChange={(e) => {
                handleDurationYearChange(e.target.value);
              }}
              name="duration"
              id="duration"
              value={selectedYear}
            >
              <MenuItem value={currentDateYear}>{currentDateYear}</MenuItem>
              <MenuItem value={prevYear}>{prevYear}</MenuItem>
              <MenuItem value={prevYearMinusTwo}>{prevYearMinusTwo}</MenuItem>
              <MenuItem value={prevYearMinusThree}>{prevYearMinusThree}</MenuItem>
            </Select>

            <Select
              onChange={(e) => {
                handleMonthChange(e.target.value);
              }}
              name="selectedMonth"
              id="selectedMonth"
              value={selectedMonth}
            >
              <MenuItem value="YTD">YTD (Year-to-Date)</MenuItem>
              <MenuItem value="January">January</MenuItem>
              <MenuItem value="February">February</MenuItem>
              <MenuItem value="March">March</MenuItem>
              <MenuItem value="April">April</MenuItem>
              <MenuItem value="May">May</MenuItem>
              <MenuItem value="June">June</MenuItem>
              <MenuItem value="July">July</MenuItem>
              <MenuItem value="August">August</MenuItem>
              <MenuItem value="September">September</MenuItem>
              <MenuItem value="October">October</MenuItem>
              <MenuItem value="November">November</MenuItem>
              <MenuItem value="December">December</MenuItem>
            </Select>

              {
                hasRole(["Admin","Super User"])?
                <PrimaryButton
              onClick={handleAddHospital}
              processingText={'Submitting'}
              iconClass={<PlusSignIcon />}
              buttonText={'Add Hospital'}
            />
                :
                <></>
              }

            {isPopupOpen && (
              <AddHospitalPopup
                onClose={handleClosePopup}
                onSubmit={handlePopupSubmit}
              />
            )}
          </div>
        )}

        {activeTab === 'analytics' && selectedMonth === 'YTD' && (
          <div className="ytd-indicator">
            {isLoadingYTD ? (
              <>⏳ Loading Year-to-Date data...</>
            ) : (
              <>
                📊 Displaying Year-to-Date data (January - {
                  Number(selectedYear) === new Date().getFullYear()
                    ? new Date().toLocaleString('default', { month: 'long' })
                    : 'December'
                })
              </>
            )}
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="total-hospital-cards">
            <DashboardTotalCard
              className="total-card"
              icon={
                <Timer02Icon size={24} color={"#07AEEF"} variant={"stroke"} />
              }
              value={
                totalData.total_acute ? (
                  <h2>{totalData.total_acute}</h2>
                ) : (
                  <span>No Available Data</span>
                )
              }
              name={"Total Acute"}
            />
            <DashboardTotalCard
              className="total-card"
              icon={<BedIcon size={24} color={"#FFB60A"} variant={"stroke"} />}
              value={
                totalData.total_swing_bed ? (
                  <h2>{totalData.total_swing_bed}</h2>
                ) : (
                  <span>No Available Data</span>
                )
              }
              name={"Total Swingbed"}
            />
            <DashboardTotalCard
              className="total-card"
              icon={
                <SearchingIcon size={24} color={"#FC7D75"} variant={"stroke"} />
              }
              value={
                totalData.total_observation ? (
                  <h2>{totalData.total_observation}</h2>
                ) : (
                  <span>No Available Data</span>
                )
              }
              name={"Total Observation"}
            />
            <DashboardTotalCard
              className="total-card"
              icon={
                <WorkoutRunIcon
                  size={24}
                  color={"#0E76BC"}
                  variant={"stroke"}
                />
              }
              value={
                totalData.total_emergency_room ? (
                  <h2>{totalData.total_emergency_room}</h2>
                ) : (
                  <span>No Available Data</span>
                )
              }
              name={"Total Emergency Room"}
            />
          </div>
        )}

        {activeTab === 'analytics'?
        (barData.measures_data !== null ?
          <div className="table-container">
            {isOverlayVisible && <TableLoading />}
            <BarGraph data={barData} year={selectedYear}/>
          </div>
          : <p>No Data Available</p>)
        :<></>}
        {activeTab === 'allHospitals' && (
          <div className='tab-contents-hospitals table-container'>
            {isOverlayVisible && <TableLoading />}
            <Hospitals />
          </div>
        )}
</div>


      </div>
    );

}
const HospitalsPage = () => {
    return (
        <DashboardContainer content={<HospitalsPageContent />} pageTitle={'All hospitals'} />
    )
}
export default HospitalsPage









