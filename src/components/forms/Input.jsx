import { useEffect, useState } from 'react';
import '../../assets/css/components/forms/forms.css';
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs from 'dayjs';


export const TextInput = ({ iconClass, label, name, type, id, placeholder, value, setValue, disabled }) => {
    const [isActive, setIsActive] = useState(false)
    const handleIsActive = () => {
        setIsActive(!isActive)
        document.querySelectorAll('.input').forEach(input => input.classList.remove('active'));
        document.getElementById(`input${id}`).classList.add('active');
    }
    return (
        <div className="field">
            <label htmlFor={id}>{label}</label>
            <div onClick={handleIsActive} className="input" id={`input${id}`}>
                {iconClass}
                <input value={value} onChange={(e) => setValue(e.target.value)} type={type || 'text'} name={name} id={id} placeholder={placeholder} disabled={disabled} />
            </div>
        </div>
    )
}
export const DataListInput = ({ id, name, label, placeholder, iconClass, options = [], value, setValue }) => {
    const [optionsOpen, setOptionsOpen] = useState(false);
    const [filteredOptions, setFilteredOptions] = useState(options);

    useEffect(() => {
        setFilteredOptions(options); 
    }, [options]);

    const handleInputChange = (e) => {
        const inputValue = e.target.value;
        setValue(inputValue);
        
        const filtered = options.filter(option =>
            option.toLowerCase().includes(inputValue.toLowerCase())
        );

        setFilteredOptions(filtered);
        setOptionsOpen(true);
    };

    const handleSelectOption = (option) => {
        setValue(option);
        setOptionsOpen(false);
    };

    
    return (
        <div className="field">
            {label && <label htmlFor={name}>{label}</label>}
            <div className="input">
                {iconClass}
                <input
                    type="text"
                    placeholder={placeholder}
                    value={value}
                    onChange={handleInputChange}
                    onFocus={() => setOptionsOpen(true)}
                    onBlur={() => setTimeout(() => setOptionsOpen(false), 200)} 
                />
            </div>

            {optionsOpen && (
                <div className="options">
                    {filteredOptions.length > 0 ? (
                        filteredOptions.map((option, index) => (
                            <p className="option" key={index} onClick={() => handleSelectOption(option)}>
                                {option}
                            </p>
                        ))
                    ) : (
                        <p className="no-data">No matching data</p> 
                    )}
                </div>
            )}
        </div>
    );
};

const DateInputMindate = dayjs('2022-01-01');
const DateInputMaxdate = dayjs('2022-01-01');
export const DateInput = ({ date, setDate, choices }) => {
    return (
        <div className="field">
            <div className="input date-input">
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                        value={date}
                        onChange={(newValue) => setDate(newValue)}
                        // views={choices}
                        minDate={DateInputMindate}

                    />
                </LocalizationProvider>
            </div>
        </div>
    )
}

const currentYear = dayjs();
const mindate = dayjs('2023-01-01');
export const YearInput = ({ year, setYear }) => {

    return (
        <div className="field">
            <div className="input date-input">
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                        label={'"year"'}
                        openTo="year"
                        maxDate={currentYear}
                        minDate={mindate}
                        yearsOrder="desc"
                        value={year}
                        views={['year']}
                        onChange={(newValue) => setYear(newValue)}

                    />
                </LocalizationProvider>
            </div>
        </div>
    )
}

