.search-and-filters {
    width: 100%;
    margin-bottom: 20px;
}

/* Stacked layout (default) */
.search-filters-stacked {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.search-filters-stacked .search-section {
    width: 100%;
}

.search-filters-stacked .filters-section {
    width: 100%;
}

/* Inline layout */
.search-filters-inline {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.search-filters-inline .filters-section .standard-filters {
    background-color: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.search-filters-inline .filters-section .filters-content {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

/* Side by side layout */
.search-filters-side-by-side {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 20px;
    align-items: start;
}

.search-filters-side-by-side .search-section {
    min-width: 300px;
}

.search-filters-side-by-side .filters-section {
    min-width: 250px;
}

/* Responsive design */
@media (max-width: 1024px) {
    .search-filters-side-by-side {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}

@media (max-width: 768px) {
    .search-and-filters {
        margin-bottom: 16px;
    }
    
    .search-filters-stacked,
    .search-filters-inline {
        gap: 12px;
    }
    
    .search-filters-inline .filters-section .standard-filters {
        padding: 12px;
    }
    
    .search-filters-inline .filters-section .filters-content {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
}

@media (max-width: 480px) {
    .search-and-filters {
        margin-bottom: 12px;
    }
    
    .search-filters-stacked,
    .search-filters-inline {
        gap: 8px;
    }
}

/* Compact variant */
.search-and-filters.compact .search-filters-stacked,
.search-and-filters.compact .search-filters-inline {
    gap: 12px;
}

.search-and-filters.compact .filters-section .standard-filters {
    padding: 12px;
}

/* Full width variant */
.search-and-filters.full-width .search-section .standard-search-bar {
    max-width: 100%;
}
