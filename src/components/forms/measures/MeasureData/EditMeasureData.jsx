import { useState, useEffect, useRef } from 'react';
import { TextInput, DataListInput, DateInput } from '../../Input';
import { PrimaryButton, SecondaryButton } from '../../../../components/forms/buttons';
import toast from 'react-hot-toast';
import api, { API_URL } from '../../../../api';
import "../../../../assets/css/profile/profile.css";
import { Flag01Icon } from "hugeicons-react";
import dayjs from 'dayjs';
import { formatDate } from '../../../../services/formatDate';


const EditMeasureData = ({ measure_data_id, measureData, onClose, onSubmit,selectedYear }) => {
    const [formData, setFormData] = useState({
        hospital: '',
        measure: '',
        value: '',
        starting_date: '',
        end_date: '',
        // reporting_date: '',
        // type: ''
    });
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    // const [reportingDate, setReportingDate] = useState(null);
    const [startingDate, setStartingDate] = useState(null);
    const [endingDate, setEndingDate] = useState(null);
    const [measureOptions, setMeasureOptions] = useState([]);
    // const [typeOptions] = useState(['numerical', 'percentage']);

    const popupRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

    useEffect(() => {
        const fetchMeasures = async (year) => {
            try {

                const response = await api.get(`${API_URL}/measures/`,
                    { params: { measure_year: year }}
                );
                setMeasureOptions(response.data);
            } catch (error) {
                console.error('Failed to fetch measures:', error);
                toast.error('Failed to load measures');
            }
        };


        fetchMeasures(selectedYear);

        if (measureData && Object.keys(measureData).length > 0) {
            setFormData({
                hospital: measureData.hospital || '',
                measure: measureData.name || '',
                value: measureData.value || '',
                // type: measureData.type || '',
                // reporting_date: measureData.reporting_date ? dayjs(measureData.reporting_date).format('YYYY-MM-DD') : '' ,
                starting_date: measureData.starting_date ? dayjs(measureData.starting_date).format('YYYY-MM-DD') : '',
            end_date: measureData.end_date ? dayjs(measureData.end_date).format('YYYY-MM-DD') : '',
            });
            // setReportingDate(measureData.reporting_date ? dayjs(measureData.reporting_date) : null);
            setStartingDate(measureData.starting_date ? dayjs(measureData.starting_date) : null);
            setEndingDate(measureData.end_date ? dayjs(measureData.end_date) : null);

            const year = dayjs(measureData.starting_date).year();
        fetchMeasures(year);
        }
    }, [measureData, selectedYear]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSaving(true);
        setError('');
        setSuccessMessage('');

        if (!formData.hospital || !formData.measure || !formData.value || !formData.starting_date || !formData.end_date) {
            setError('All fields are required.');
            setIsSaving(false);
            return;
        }

        try {
            const response = await api.put(
                `${API_URL}/measures/update_measure_data_api/${measure_data_id}/`,formData,
            );

            if (response.status === 200) {
                toast.success('Measure updated successfully!');
                // Close the popup first to prevent loading overlay
                onClose();
                // Then update the data if needed
                if (onSubmit) {
                    // Use setTimeout to ensure the popup is closed before fetching data
                    setTimeout(() => {
                        onSubmit();
                    }, 100);
                }
            } else {
                toast.error('Failed to update measure');
            }
        } catch (error) {
            console.error('API error:', error.response ? error.response.data : error.message);
            const errorMessage = error.response ? error.response.data.error : error.message;
            toast.error(errorMessage);
        } finally {
            setIsSaving(false);
        }
    };

    const handleCancel = (e) => {
        e.preventDefault();
        onClose();
    };

    const handleDateChange = (date, name) => {

        const formattedDate = formatDate(date)
        setFormData(prevFormData => ({
            ...prevFormData,
            [name]: formattedDate,
        }));
    };

    return (
        <div className="overlay">
            <div className="popup-user" ref={popupRef}>
                <form onSubmit={handleSubmit}>
                    <h2>Edit Measure Data</h2>
                    {error && <div className="error-message">{error}</div>}
                    {successMessage && <div className="success-message">{successMessage}</div>}

                    <TextInput

                        id="hospital"
                        name="hospital"
                        placeholder="Hospital"
                        value={formData.hospital}
                        setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, hospital: value }))}
                    />

                    <DataListInput
                        iconClass={<Flag01Icon />}
                        id="measure"
                        name="measureName"
                        placeholder="Measure name"
                        value={formData.measure}
                        options={measureOptions.map(measure => measure.name)}
                        setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, measure: value }))}
                    />


                    {/* <DataListInput
                        id="type"
                        name="measureType"
                        placeholder="Select Type"
                        value={formData.type}
                        options={typeOptions}
                        setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, type: value }))}
                    /> */}
                    <TextInput
                        id="value"
                        name="value"
                        placeholder="Value"
                        type="number"
                        value={formData.value}
                        setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, value: value }))}
                    />

                    {/* <DateInput
                        id="reporting_date"
                        name="reporting_date"
                        setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, reporting_date: dayjs(value).format('YYYY-MM-DD') }))}
                        value={reportingDate ? dayjs(reportingDate) : null}
                        date={reportingDate}
                        setDate={(newDate) => setReportingDate(newDate)}
                        placeholder="Reporting date"
                        label="Reporting date"
                        choices={['day', 'month', 'year']}
                    /> */}
                    <div className="dateInput">
                        <div className='label-dates'>
                            <label>Start Date</label>
                            <div
                                onMouseDown={(e) => e.stopPropagation()} // Prevent popup close
                                onClick={(e) => e.stopPropagation()} // Prevent popup close
                            >
                                <DateInput
                                    value={startingDate ? dayjs(startingDate) : null}
                                    //   setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, starting_date: dayjs(value).format('YYYY-MM-DD') }))}
                                    placeholder={"Start Date"}
                                    setDate={(newDate) => handleDateChange(newDate, "starting_date")}
                                    date={startingDate}
                                    label={"Start Date"}
                                    choices={["day", "month", "year"]}
                                />
                            </div>
                        </div>

                        <div className='label-dates'>
                            <label>End Date</label>
                            <div
                                onMouseDown={(e) => e.stopPropagation()} // Prevent popup close
                                onClick={(e) => e.stopPropagation()} // Prevent popup close
                            >
                                <DateInput
                                    value={endingDate ? dayjs(endingDate) : null}
                                    //   setValue={(value) => setFormData((prevFormData) => ({ ...prevFormData, end_date: dayjs(value).format('YYYY-MM-DD') }))}
                                    placeholder={"End Date"}
                                    date={endingDate}
                                    setDate={(newDate) => handleDateChange(newDate, "end_date")}
                                    label={"End Date"}
                                    choices={["day", "month", "year"]}
                                />
                            </div>
                        </div>

                    </div>

                    <div className="popup-buttons">
                        <SecondaryButton onClick={handleCancel} type="button" buttonText="Cancel" />
                        <PrimaryButton isSubmitting={isSaving} buttonText="Save" type="submit" />
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditMeasureData;
