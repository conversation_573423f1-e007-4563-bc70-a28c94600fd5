import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuthentication } from "./authContext";
import SessionExpired from "../pages/auth/SessionExpired";
import PageLoading from "../components/loading/PageLoading";

function ProtectedRoute({ children }) {
    const { loading, isAuth } = useAuthentication();
    const location = useLocation();

    if (loading) {
        return <PageLoading />;
    }

    if (!isAuth) {
        // Redirect to login with the attempted location
        return <Navigate to="/login" state={{ from: location }} replace />;
    }

    return children;
}

export default ProtectedRoute;
