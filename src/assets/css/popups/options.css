.position-data {
  background-color: #EBEBEB;
  width: 200px;
  gap: 12px;
  border-radius: 8px;
  padding: 20px;
  top: 280px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
.position-data .action-button {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.position-data .action-button .line {
  width: 100%;
  height: 1px;
  border: 1px solid gray;
  background-color: #6c6565;
}
.position-data .action-button p {
  display: flex;
  align-items: center;
  margin: 0;
  gap: 8px;
  cursor: pointer;
}/*# sourceMappingURL=options.css.map */