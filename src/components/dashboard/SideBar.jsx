import React, { useEffect, useState,useMemo } from 'react'
import '../../assets/css/components/dashboardContainer/dashboardContainer.css';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import api, { API_URL, siteTitle } from '../../api';
import { Analytics02Icon, ArrowDown01Icon, ArrowUp01Icon, Logout01Icon, Briefcase01Icon, Cancel01Icon, ChartIcon, CircleIcon, DashboardSquare01Icon, Hospital01Icon, Note01Icon, Note05Icon, Notification02Icon, Target01Icon, UserIcon, UserMultiple02Icon, UserSettings01Icon } from 'hugeicons-react';
import { hasRole } from '../../services/userPosition';
import Cookies from 'js-cookie';


const Dropdown = ({ options, isOpen, toggleDropdown }) => {
    const location = useLocation();
    const currentPath = location.pathname;

    return (
        <div className="drop-down">
            <div className="drop-down-title" onClick={toggleDropdown}>
                <div className="title">
                    <Hospital01Icon size={24} />
                    <span>Hospitals</span>
                </div>
                {isOpen ? (
                    <ArrowUp01Icon className="drop-down-icon" />
                ) : (
                    <ArrowDown01Icon className="drop-down-icon" />
                )}
            </div>
            {isOpen && (
                <div className="drop-down-content">
                    {options.map((option, index) => (
                        <Link
                            key={index}
                            to={option.path}
                            className={`menu-item ${currentPath === option.path ? 'active' : ''}`}
                            // onClick={() => setMobileMenuOpen(false)}
                        >
                            {option.icon ? (
                                <div className="initials">{option.icon}</div>
                            ) : (
                                <CircleIcon size={12} />
                            )}
                            <span>{option.name}</span>
                        </Link>
                    ))}
                </div>
            )}
        </div>
    );
};

const SideBar = ({ mobileMenuOpen, setMobileMenuOpen }) => {
    const location = useLocation();
    const currentPath = location.pathname;
    const [position, setPosition] = useState('');
    const [hospital, setHospital] = useState('');
    const [hospitalId, setHospitalId] = useState('');
    const [path, setPath] = useState('');
    const [isSingleHospital, setIsSingleHospital] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    const navigate = useNavigate();

    const [hospitalsList, setHospitalsList] = useState([]);

    const fetchHospitalList = async () => {
        try {
            const response = await api.get(`${API_URL}/hospitals/`);
            setIsSingleHospital(false);
            if (response.status === 200) {
                const data = response.data;
                setHospitalsList([{
                    'icon': 'A', 'name': 'All Hospitals', 'path': '/hospitals/'
                }]);

                // Check if data is an array and has items
                if (Array.isArray(data) && data.length > 0) {
                    data.forEach((hospital, index) => {
                        // Add null checks for hospital properties
                        if (hospital && hospital.name && hospital.id) {
                            const icon = hospital.name.charAt(0);
                            const name = hospital.name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                            const hospitalId = hospital.id;
                            const path = `/hospitals/${name}/${hospitalId}/`;

                            const item = {
                                'icon': icon, 'name': name, 'path': path, "hospitalId": hospitalId
                            };

                            setHospitalsList(prevHospital => [...prevHospital, item]);
                        }
                    });
                } else if (data && typeof data === 'object') {
                    // Handle object format
                    for (let hospital in data) {
                        if (data[hospital] && data[hospital].name && data[hospital].id) {
                            const icon = data[hospital].name.charAt(0);
                            const name = data[hospital].name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                            const hospitalId = data[hospital].id;
                            const path = `/hospitals/${name}/${hospitalId}/`;

                            const item = {
                                'icon': icon, 'name': name, 'path': path, "hospitalId": hospitalId
                            };

                            setHospitalsList(prevHospital => [...prevHospital, item]);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching hospital list:', error);
            setIsSingleHospital(false);
        }
    };

    useEffect(() => {
        const fetchUserPermission = async () => {
            setPosition(localStorage.getItem("position"));
            setHospital(localStorage.getItem("hospital"));
            setHospitalId(localStorage.getItem("hospital_id"));

            if (hasRole(["User", "User Editor","Manager"])) {
                setPath(`/hospitals/${localStorage.getItem("hospital")}/${localStorage.getItem("hospital_id")}/`);
                setIsSingleHospital(true);
            } else {
                setPath(`/`);
                fetchHospitalList();
            }
        };

        fetchUserPermission();
    }, []);

    const handleLogout = () => {
        localStorage.clear();
        Cookies.remove('accessToken');
        Cookies.remove('refreshToken');
        window.location.href = process.env.REACT_APP_DOMAIN_NAME || '/login';
    };
    const isMobileView = () => window.innerWidth <= 800;
    const handleMenuClick = () => {
        if (isMobileView()) {
            setMobileMenuOpen(false);
        }
    };

    const toggleDropdown = () => {
        const newState = !isDropdownOpen;
        setIsDropdownOpen(newState);
        localStorage.setItem('isDropdownOpen', newState);

    };


    return (
        <div className={`sidebar-logout ${mobileMenuOpen ? 'show' : 'hidden'}`}>
            <div className={`side-bar ${mobileMenuOpen ? 'show' : 'hide'}`}>
                <Link to="/" className='branding'>
                    <img src="/ch-white-logo.svg" alt="logo" />
                    <h2 className='site-title'>{siteTitle}</h2>
                </Link>

                <Cancel01Icon
                    size={32}
                    className="close-menu"
                    onClick={() => setMobileMenuOpen(false)}

                />

                <div className="menu-items">
                    <Link to={path} className={`menu-item ${currentPath === path ? 'active' : ''}`} onClick={handleMenuClick}>
                        <DashboardSquare01Icon size={24} />
                        <span> Dashboard</span>
                    </Link>

                            <Link to="/users/" className={`menu-item ${currentPath === '/users/' ? 'active' : ''}`} onClick={handleMenuClick}>
                        <UserMultiple02Icon size={24} />
                        <span>Users</span>
                    </Link>

                    {
                        !isSingleHospital && (
                            <Dropdown
                                options={hospitalsList}
                                isOpen={isDropdownOpen}
                                toggleDropdown={toggleDropdown}
                                onClick={handleMenuClick}
                            />
                        )
                    }

                    <Link to="/measures/" className={`menu-item ${currentPath === '/measures/' ? 'active' : ''}`} onClick={handleMenuClick}>
                        <Analytics02Icon size={24} />
                        <span>Measures</span>
                    </Link>

                        <Link to="/measure_data/" className={`menu-item ${currentPath === '/measure_data/' ? 'active' : ''}`} onClick={handleMenuClick}>
                        <Analytics02Icon size={24} />
                        <span>Measure Data</span>
                    </Link>


                            <Link to="/positions/" className='menu-item' onClick={handleMenuClick}>
                        <Briefcase01Icon size={24} />
                        <span>Roles & Permissions</span>
                    </Link>


                    <Link to="/targets/" className={`menu-item ${currentPath === '/targets/' ? 'active' : ''}`} onClick={handleMenuClick}>
                        <Target01Icon size={24} />
                        <span>Targets</span>
                    </Link>

                    {
                        hasRole(["Admin", "Super User"]) &&

                        <Link to="/notifications/" className={`menu-item ${currentPath === '/notifications/' ? 'active' : ''}`} onClick={handleMenuClick}>
                        <Notification02Icon size={24} />
                        <span>Notifications</span>
                    </Link>
                    }


                    <div className="profile-item" onClick={handleLogout}>
                        <Logout01Icon size={24} color={"#ffffff"} variant={"stroke"} />
                        <span>Logout</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SideBar;