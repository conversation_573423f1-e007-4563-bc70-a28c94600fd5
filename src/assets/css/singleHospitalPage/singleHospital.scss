@import "../variables.scss";

.single-hospitals-page {
  .main-container {

    .hospitals-container {
      .charts {
        display: grid;
        grid-template-columns: 50% 50%;
        // display: flex;
        // flex-wrap: wrap;
        gap: 20px;

        .bar-chart-container,
        .pie-chart-container {
          // display: flex;
          flex: 1 1 30vw;
          padding: 1rem;
          border: 1px solid #e5e5e5;
          border-radius: 8px;

          .bar-chart {
            .filter {
              display: flex;
              gap: 16px;
              align-items: center;

              h2{
                font-size: 18px; 
                font-family: 'Rubik', sans-serif;
              }
            }

            .css-1no7sbt-MuiBarElement-root:hover{
              fill: #07AEEF;
            }
          }

          .pie-chart{
            display: grid;
    gap: 10px;
    grid-template-columns: 50% 50%;
    align-items: center;
          }

          .statistics{
            .title{
              p{
                color: #999999
              }
              padding-bottom: 10px;
              border-bottom: 1px solid #E5E5E5;
            }

            .center-label{
              display: flex;
              flex-direction: column;
              transform: translate(-50%, -50%);
              position: absolute;
              top: 50%;
              left: 31%;
              text-align: center;

              .label{
                color: #999999;
              }

              .number{
                font-size: 18px;
                font-weight: 700;
              }
            }

            
          }
        }
        .bar-chart-container{
          flex: 5 1 300px;
        //  background-color: #cadde4;
        }
      }


    }

    .overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.7);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 999;

      .edit-hospital-popup {
        padding: 32px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        max-width: 530px;

        width: 100%;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        gap: 24px;

        .buttons {
          display: flex;
          gap: 16px;
          justify-content: flex-end;
        }

        .banner {
          background-color: #adf4ce;
          border-radius: 8px;
          padding: 16px;
        }

        .date {
          width: 100%;
        }
      }
    }
  }
}

@media screen and (max-width: 1026px){
  .single-hospitals-page {
    .main-container {
  
      .hospitals-container {
        .charts {
          grid-template-columns: 100%;

          .bar-chart-container,
        .pie-chart-container {
          .pie-chart{
            grid-template-columns: 100%;
          }
        }
        }
      }
    }
  }
  
}
