import React, { useEffect, useState } from "react";

import Cookies from 'js-cookie';
import api, { API_URL } from "../../api";
import { PrimaryButton } from "../../components/forms/buttons";
import DashboardContainer from "../../components/dashboard/DashboardContainer";
import {Delete01Icon, Edit02Icon, PlusSignIcon} from "hugeicons-react";
import AddHospitalListPopup from "../../components/hospitals/AddHospitalListPopup ";
import EditHospitalPopup from "../../components/hospitals/EditHospitalPopup";
import DeleteHospitalPopup from "../../components/hospitals/DeleteHospitalPopup";
import { isAdminOrSuperUser } from "../../services/userPosition";
import TableLoading from "../../components/loading/TableLoading";


const HospitalsContent = () => {
  const [hospitals, setHospitals] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedHospital, setSelectedHospital] = useState(null);
const [editPopupOpen, setEditPopupOpen] = useState(false);
const [deletePopupOpen, setDeletePopupOpen] = useState(false);


  const fetchHospitals = async () => {
    setIsLoading(true);
    try {
      const response = await api.get(`${API_URL}/hospitals/`);
      if (response.status === 200) {
        console.log('Hospitals data:', response.data);
        setHospitals(response.data);
      }
    } catch (error) {
      console.error('Error fetching hospitals:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchHospitals();
  }, []);

  const handleAddHospital = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  const handleAddHospitalToList = (newHospital) => {
    setHospitals((prevHospitals) => [...prevHospitals, newHospital])
  };
  const handleEditHospitalToList = (updatedHospital) => {
    setHospitals((prevHospitals) =>
      prevHospitals.map((h) => (h.id === updatedHospital.id ? updatedHospital : h))
    );
  };


  const handleDeleteHospitalFromList = (hospitalId) => {
    setHospitals((prevHospitals) => prevHospitals.filter((h) => h.id !== hospitalId));
  };

  return (
<div className="hospital-content">
      <div className='select-button'>

        {
                        isAdminOrSuperUser?
                        <PrimaryButton
                      onClick={handleAddHospital}
                      // processingText={'Submitting'}
                      iconClass={<PlusSignIcon />}
                      buttonText={'Add Hospital'}
                    />
                        :
                        <></>
                      }
        {isPopupOpen && (
<AddHospitalListPopup
            onClose={handleClosePopup}
            onAddHospital={handleAddHospitalToList}
          />
        )}
      </div>
      {hospitals.length > 0 ? (
        <div className="table-container">
          {isLoading && <TableLoading />}
          <table>
            <thead>
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Date Created</th>
                <th>Address</th>
                <th>Phone Number</th>
                <th>CCN Number</th>
                {
                  isAdminOrSuperUser?
                  <th>Action</th>
                  :

                  <></>
                }

              </tr>
            </thead>
            <tbody>
          {editPopupOpen && (
      <EditHospitalPopup
        hospital={selectedHospital}
        onClose={() => setEditPopupOpen(false)}
        onUpdateHospital={handleEditHospitalToList}
        fetchHospitals={fetchHospitals}
      />
    )}

    {deletePopupOpen && (
  <DeleteHospitalPopup
    hospital={selectedHospital}
    onClose={() => setDeletePopupOpen(false)}
    onDeleteHospital={handleDeleteHospitalFromList}
  />
)}


            {hospitals.map((hospital) => (
              <tr key={hospital.id}>
                <td>{hospital.id}</td>
                <td>{hospital.name}</td>
                <td>{new Date(hospital.date_created).toLocaleDateString()}</td>
                <td>{hospital.address}</td>
                <td>{hospital.phonenumber}</td>
                <td>{hospital.cnn}</td>

                {
                  isAdminOrSuperUser?
                  <td>
                    
                    <div className="action-buttons">
                      <Edit02Icon
                      size={32}
                        className="edit-icon"
                        onClick={() => { setSelectedHospital(hospital); setEditPopupOpen(true); }}
                      />
                      <Delete01Icon
                      size={32}
                        className="delete-icon"
                        onClick={() => { setSelectedHospital(hospital); setDeletePopupOpen(true); }}
                      />
                    </div>
                  </td>
                  :
                  <></>
                }

              </tr>
            ))}
          </tbody>
        </table>
        </div>
      ) : (
        <p>Hospitals</p>
      )}
    </div>
  );
};


const Hospitals = () => {
  return (
      <DashboardContainer content={<HospitalsContent />} pageTitle={'hospitals'} />
  )
}
export default Hospitals



