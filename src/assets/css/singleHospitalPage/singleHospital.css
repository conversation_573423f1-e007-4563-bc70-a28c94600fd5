.single-hospitals-page .main-container .hospitals-container .charts {
  display: grid;
  grid-template-columns: 50% 50%;
  gap: 20px;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container {
  flex: 1 1 30vw;
  padding: 1rem;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .bar-chart .filter,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .bar-chart .filter {
  display: flex;
  gap: 16px;
  align-items: center;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .bar-chart .filter h2,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .bar-chart .filter h2 {
  font-size: 18px;
  font-family: "Rubik", sans-serif;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .bar-chart .css-1no7sbt-MuiBarElement-root:hover,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .bar-chart .css-1no7sbt-MuiBarElement-root:hover {
  fill: #07AEEF;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .pie-chart,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .pie-chart {
  display: grid;
  gap: 10px;
  grid-template-columns: 50% 50%;
  align-items: center;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .statistics .title,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .statistics .title {
  padding-bottom: 10px;
  border-bottom: 1px solid #E5E5E5;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .statistics .title p,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .statistics .title p {
  color: #999999;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .statistics .center-label,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .statistics .center-label {
  display: flex;
  flex-direction: column;
  transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 31%;
  text-align: center;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .statistics .center-label .label,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .statistics .center-label .label {
  color: #999999;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .statistics .center-label .number,
.single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .statistics .center-label .number {
  font-size: 18px;
  font-weight: 700;
}
.single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container {
  flex: 5 1 300px;
}
.single-hospitals-page .main-container .overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.single-hospitals-page .main-container .overlay .edit-hospital-popup {
  padding: 32px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  max-width: 530px;
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.single-hospitals-page .main-container .overlay .edit-hospital-popup .buttons {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}
.single-hospitals-page .main-container .overlay .edit-hospital-popup .banner {
  background-color: #adf4ce;
  border-radius: 8px;
  padding: 16px;
}
.single-hospitals-page .main-container .overlay .edit-hospital-popup .date {
  width: 100%;
}

@media screen and (max-width: 1026px) {
  .single-hospitals-page .main-container .hospitals-container .charts {
    grid-template-columns: 100%;
  }
  .single-hospitals-page .main-container .hospitals-container .charts .bar-chart-container .pie-chart,
  .single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .pie-chart {
    grid-template-columns: 100%;
  }
}/*# sourceMappingURL=singleHospital.css.map */