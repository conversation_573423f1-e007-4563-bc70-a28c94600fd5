import React, { useState } from 'react'
import { DateInput, TextInput } from '../Input'
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { PrimaryButton } from '../buttons';
import toast from 'react-hot-toast';


const NewNotificationForm = ({ setShowNewNotificationFrom }) => {
    const [name, setName] = useState('')
    const [target, setTarget] = useState('')
    const [NotificationingDate, setNotificationingDate] = useState(null)
    const [isSaving, setIsSaving] = useState(false)
    const [formMessage, setFormMessage] = useState()

    const handleNewNotificationForm = () => {

        if (!name || !target || !NotificationingDate) {
            toast.error('Please fill all fields')
            return
        }
        setIsSaving(true)
        setTimeout(() => {
            setIsSaving(false)
            setShowNewNotificationFrom(false)
        }, 2000);
    }
    return (
        <div className='form'>
            <h2>New Notification</h2>
            <form>
                <TextInput type={'text'} name={"NotificationName"} placeholder={'Notification name'} value={name} setValue={setName} />
                <TextInput type={'number'} name={"NotificationTarget"} placeholder={'Notification target'} value={target} setValue={setTarget} />
                <DateInput date={NotificationingDate} setDate={setNotificationingDate} choices={["month", 'year']} />
            </form>

            <div className="buttons">
                <PrimaryButton buttonText={'Save'} onClick={handleNewNotificationForm} isLoading={isSaving} processingText={'Saving'} />

            </div>
        </div>
    )
}

export default NewNotificationForm
