// Action buttons improvements - Global styles
.action-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;

  svg {
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      transform: scale(1.1);
    }

    &.edit-icon {
      color: #28a745;

      &:hover {
        background-color: #d4edda;
        color: #155724;
      }
    }

    &.delete-icon {
      color: #dc3545;

      &:hover {
        background-color: #f8d7da;
        color: #721c24;
      }
    }
  }
}

// Role filter container
.role-filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-filter-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: #e9ecef;
    border-color: #adb5bd;
  }

  svg {
    color: #6c757d;
  }
}

// Form field styling
.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;

  label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }
}
