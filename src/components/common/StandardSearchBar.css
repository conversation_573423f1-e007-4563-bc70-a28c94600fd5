.standard-search-bar {
    display: flex;
    align-items: center;
    gap: 0;
    width: 100%;
    max-width: 600px;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.search-input-container {
    flex: 1;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: none;
    outline: none;
    font-size: 14px;
    color: #374151;
    background-color: transparent;
    font-family: "Plus Jakarta Sans", sans-serif;
}

.search-input::placeholder {
    color: #9ca3af;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    background-color: transparent;
}

.search-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background-color: #07AEEF;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    font-family: "Plus Jakarta Sans", sans-serif;
    transition: background-color 0.2s ease;
    white-space: nowrap;
}

.search-button:hover:not(:disabled) {
    background-color: #0891d1;
}

.search-button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

.search-button svg {
    flex-shrink: 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .standard-search-bar {
        max-width: 100%;
    }
    
    .search-input {
        padding: 10px 12px;
        font-size: 14px;
    }
    
    .search-button {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .search-button span {
        display: none;
    }
}

@media (max-width: 480px) {
    .search-button {
        padding: 10px 12px;
    }
}

/* Focus states */
.standard-search-bar:focus-within {
    box-shadow: 0 0 0 3px rgba(7, 174, 239, 0.1);
    border-color: #07AEEF;
}

/* Variants */
.standard-search-bar.compact {
    max-width: 400px;
}

.standard-search-bar.full-width {
    max-width: 100%;
}

.standard-search-bar.small .search-input {
    padding: 8px 12px;
    font-size: 13px;
}

.standard-search-bar.small .search-button {
    padding: 8px 16px;
    font-size: 13px;
}
