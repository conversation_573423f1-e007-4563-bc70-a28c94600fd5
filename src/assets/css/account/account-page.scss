@import "../variables";

.account-contents {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;

  // .empty-text{
  //   display: none;
  //   width: 100%;
  //   height: fit-content;
  //   padding: 16px;
  //   background-color: aquamarine;
  //   border-radius: 8px;
  // }

  .titleName-deleteBtn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
  }

  .profilInfo {
    width: 95%;
    display: flex;
    gap: 32px;
    justify-content: space-between;

    .profil-image-text {
      width: 200px;
      height: 240px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .profil-img {
        width: 200px;
        height: 200px;
        position: relative;
        border: 1px solid rgba(170, 167, 167, 0.27);
        border-radius: 50%;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
        }

        .camera-icon {
          width: 30px;
          height: 30px;
          position: absolute;
          bottom: 50%;
          left: 50%;
          transform: translateX(-50%);
          background-color: blue;
          color: white;
          padding: 4px;
          border-radius: 50%;
          cursor: pointer;
        }
      }
    }

    .infoForm {
      display: flex;
      flex: 1 100%;
      flex-direction: column;
      gap: 24px;
      border: 1px solid #F2F2F2;
      padding: 32px;

      .account-form {
        
        display: flex;
        flex-direction: column;
        gap: 24px;

        

        .buttons {
          display: flex;
          align-self: flex-end;
          gap: 16px;
        }
      }
    }
  }
}

.popup {
 
  .popup-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    .text-area-measure{
    padding: 24px;
    outline: none;
    border-radius: 10px;
    border: 1px solid #ebe9e9;
    font-family: sans-serif;
    font-size: 16px;
    }
    .value-type, .dateInput{
      display: flex;
      gap: 12px;
     
    }
    .field{
      width: 100%;
    }
    .text-red{
      color: red;
      font-size: 14px;
      font-weight: bold;
    }

    .popup-text-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;

      .info-icon {
        width: 36px;
        height: 40px;
        background-color: #E31A1A;
        color: #ffffff;
        border-radius: 25px;
        padding: 3px;
      }

      .popup-delete-text {
        display: flex;
        flex-direction: column;
        gap: 8px;

        p {
          font-size: 14px;
          color: #999999;
        }
      }


    }

    .buttons {
      display: flex;
      justify-content: center;
      gap: 16px;
    }
    .form-group{
      display: flex;
      gap: 12px;
    }
  }
}



@media screen and (max-width:820px) {

  .account-contents{
    .profilInfo{
      width: 100%;
      .infoForm{
        padding: 12px;

        form{
          display: flex;
          flex-direction: column;
          gap: 24px;
        }
      }

    }
  }
  
}

@media screen and (max-width:630px) {

  .account-contents{
    .empty-text{
      display: block;
    }
    .profilInfo{
      width: 100%;
      flex-direction: column;
      align-items: center;

      .profil-image-text{
        display: flex;
      }
      .infoForm{
        padding: 12px;
      }

    }
  }
  
}