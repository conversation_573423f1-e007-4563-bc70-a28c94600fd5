
export const isAdminOrSuperUser = !!((localStorage.getItem("position") === "Admin") || (localStorage.getItem("position") === "Super User") )

export const isUser = !!((localStorage.getItem("position") === "User") )

export const isAdmin = !!((localStorage.getItem("position") === "Admin") )


export const hasRole = (allowedRoles) => {
    const userRole = localStorage.getItem("position");
   
    return allowedRoles.some((role) => userRole === role);
  };