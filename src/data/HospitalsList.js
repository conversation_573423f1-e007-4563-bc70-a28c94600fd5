// export const hospitalsList = [
//     {
//         "id": "COH-HO-1",
//         "name": "Pawhuska",
//         "date_created": "2024-06-18T13:26:32.142827Z"
//     },
//     {
//         "id": "COH-HO-2",
//         "name": "Mangum",
//         "date_created": "2024-06-18T13:46:51.843056Z"
//     },
//     {
//         "id": "COH-HO-3",
//         "name": "<PERSON>",
//         "date_created": "2024-06-18T13:47:09.907947Z"
//     },
//     {
//         "id": "COH-HO-4",
//         "name": "Seiling",
//         "date_created": "2024-06-18T13:47:39.481349Z"
//     },
//     {
//         "id": "COH-HO-5",
//         "name": "Prague",
//         "date_created": "2024-06-18T13:48:13.315430Z"
//     }
// ]

// export const hospitalsCard = 
//     {
//         "acute": "55",
//         "swing": "52",
//         "observation": "11",
//         "emergency": "844",
    
//     }

//     export const hospitalData = [
//         { hospital: 'Seiling', acuteAdmissions: 6.00, observationAdmissions: 0, swingBedAdmissions: 10.00, edEncounters: 100.00, category5: 25, category6: 10 },
//         { hospital: 'Prague', acuteAdmissions: 4.00, observationAdmissions: 5.55, swingBedAdmissions: 8.00, edEncounters: 200.00, category5: 30, category6: 15 },
//         { hospital: 'Pawhuska', acuteAdmissions: 8.00, observationAdmissions: 2.00, swingBedAdmissions: 10.50, edEncounters: 250.00, category5: 20, category6: 5 },
//         { hospital: 'Magnum', acuteAdmissions: 18.00, observationAdmissions: 3.00, swingBedAdmissions: 12.00, edEncounters: 150.00, category5: 35, category6: 20 },
//         { hospital: 'Carnegie', acuteAdmissions: 17.00, observationAdmissions: 1.00, swingBedAdmissions: 13.00, edEncounters: 180.00, category5: 30, category6: 15 },
//       ];
    
//       export const categories = [
//         { key: 'acuteAdmissions', title: 'Acute Admissions', xAxisValues: [0.00, 2.00, 4.00, 6.00, 8.00, 10.00, 12.00, 14.00, 16.00, 18.00, 20.00] },
//         { key: 'observationAdmissions', title: 'Observation Admissions', xAxisValues: [0.00, 1.00, 2.00, 3.00, 4.00, 5.00, 6.00] },
//         { key: 'swingBedAdmissions', title: 'Swing Bed Admissions', xAxisValues: [0, 2.00, 4.00, 6.00, 8.00, 10.00, 12.00, 14.00] },
//         { key: 'edEncounters', title: 'ED Encounters', xAxisValues: [0.00, 50.00, 100.00, 150.00, 200.00, 250.00, 300.00] },
//         { key: 'category5', title: 'Category 5', xAxisValues: [0, 10, 20, 30, 40, 50, 60, 70] },
//         { key: 'category6', title: 'Category 6', xAxisValues: [0, 5, 10, 15, 20, 25, 30, 35] },
//       ];
     