
import React from 'react';
import DashboardContainerDoc from '../../components/dashboard/DashboardContainerDoc';
import '../../assets/css/pages/authPages/authPages.css';

const NumberText = ({ number, text }) => (
  <div className="number-text">
    <p className="number">{number}</p>
    <p>{text}</p>
  </div>
);
const DashboardContainerData = () => {
  const Section = ({ id, title, description, listing, imageSrc, imageAlt }) => (
    <div className={`section ${id}`} id={id}>
      <div className="text">
        <div className="title">
          <h1>{title}</h1>
          <p>{description}</p>
        </div>
        <div className="listing">
          {listing.map((item, index) => (
            <NumberText key={index} number={item.number} text={item.text} />
          ))}
        </div>
      </div>
      <div className="image">
        <img src={imageSrc} alt={imageAlt} />
      </div>
    </div>
  );
  return (
    
    <div>
    <Section
     id="login"
     title="Authentication"
     description="This page will be accessed by users who are not authenticated yet. Here you can sign up, sign in, and reset your password."
     listing={[
          {
            number: "1",
            text:"Open the platform, the first screen will appear with a field to fill in your email. Fill your email address then click verify button."
          },
          {
            number: "2",
            text:"The next screen is for verifying if it’s really you. You will be asked to fill the verification code sent to your email. After filling the code, you have to enter your account password."

          },
          {
            number: "3",
            text:"In case you forgot your password, there is a button below the password field labeled 'forgot password'. Click on it so that you can get a new password."

          },
        ]}
        imageSrc="/login.png"
        imageAlt="Login"
    
     />
    <Section
        id="dashboard"
        title="Dashboard"
        description="This page will be accessed by authenticated users with the right permissions, allowing them to interact with the data."
        listing={[
          {
            number: "1",
            text: "After being authenticated, you will be redirected to the dashboard page which contains an overview of the information you need. If you are a Manager for a particular hospital, you will get your hospital's information only. If you are an admin, you will get information for all hospitals.",
          },
          {
            number: "2",
            text: "You will be able to interact with this page, where based on your permissions, you can add users, measures, targets, and view information for certain hospitals.",
          },
          {
            number: "3",
            text: "On the sidebar, you'll get all the information you need to interact with the system, depending on your permissions and role.",
          },
        ]}
        imageSrc="/generalDashboard.svg"
        imageAlt="Dashboard Documentation"
      />

      {/* Hospital Dashboard Section */}
      <Section
        id="hospitals"
        title="Hospital Dashboard"
        description="This page is for the All Hospital dashboard, containing an overview for all hospitals."
        listing={[
          {
            number: "1",
            text: "This page shows you an overview of all hospitals, and you can navigate through them.",
          },
          {
            number: "2",
            text: "Admins should be able to add new hospitals and view all hospitals.",
          },
        ]}
        imageSrc="/hospital-svg.png"
        imageAlt="Hospital Dashboard"
      />

      {/* Single Hospital Dashboard Section */}
      <Section
        id="singleHospitalDetail"
        title="Single Hospital Dashboard"
        description="This page is for the single hospital dashboard, containing an overview for a single hospital."
        listing={[
          {
            number: "1",
            text: "This page shows you an overview of a single hospital, and you can interact with it.",
          },
        ]}
        imageSrc="/single-hospital.png"
        imageAlt="Single Hospital Dashboard"
      />
 {/* Single Users Section */}
 <Section
        id="userList"
        title="User List"
        description="This page contains all the user list in the system."
        listing={[
          {
            number: "1",
            text: "If you are authenticated user you will have the right to get the list for all users in system.",
          },
          {
            number: "2",
            text: "You will be able to interact with the user listing where you will be able to click on and be able to edit user details and user password according to the permission you have",
          },
        ]}
        imageSrc="/userListing.svg"
        imageAlt="Users"
      />

<Section
        id="newUser"
        title="Add New User"
        description="This page shows how admin can add new user in the system."
        listing={[
          {
            number: "1",
            text: "If you have admin permission you can add new user in the system.",
          },
          {
            number: "2",
            text: "If you are an authenticated user you can edit the details of the user also delete them.",
          },
          {
            number: "3",
            text: "When adding a user, a password will be generated and sent to on the email you provide Adding a user gives them permissions, proceed with caution!",
          },
        ]}
        imageSrc="/addNewUser.png"
        imageAlt="Users"
      />
      <Section
        id="editUser"
        title="Edit User"
        description="This page contains the form for edit user where you can change his/her recorded data."
        listing={[
          {
            number: "1",
            text: "Normally, you can't edit the user data while you are not authenticated user who has permission to do that, after clicking on edit icon you will get the form contains user informations and you will be able to change one of them or all of them after that you will click on save button then information will be edited successfully and being saved in database to replace the existing one.",
          },
          
        ]}
        imageSrc="/editUser.png"
        imageAlt="Users"
      />
       <Section
        id="deleteUser"
        title="Delete User"
        description="This is the form that will be pop up after you clicked on the delete icon."
        listing={[
          {
            number: "1",
            text: "After getting that popup form you will be able to delete the selected User, after clicked on delete user will be deleted from database successful and on the page too.",
          },
          
        ]}
        imageSrc="/deleteUser.png"
        imageAlt="Users"
      />
       <Section
        id="userDetails"
        title="User Details"
        description="This page holds the specific details of a single user. If you're on the user listing page and click on an individual user, you'll be redirected to this page."
        listing={[
          {
            number: "1",
            text: "On this page, you can initially view the user details.",
          },
          {
            number: "2",
            text: "Then again, you can change the user's password by clicking on the 'Change Password' button located in the top right corner of the page.",
          },
          
        ]}
        imageSrc="/user-details.svg"
        imageAlt="Users"
      />
       <Section
        id="editDetails"
        title="Edit User Details"
        description="This page will show you the form for editing user details if you are authenticated user."
        listing={[
          {
            number: "1",
            text: "This page displays a form for editing user details. You can modify the information you want to change, then save the updates or choose to cancel if you wish.",
          },
          
          
        ]}
        imageSrc="/changePassword.png"
        imageAlt="Users"
      />
     <Section
        id="changePassword"
        title="Change user password"
        description="This page will show you the form for change user password if you are authenticated user."
        listing={[
          {
            number: "1",
            text: "This page gives you a form to change your password. You can adjust the password as you like and then save the changes or cancel if you prefer.",
          },
          
          
        ]}
        imageSrc="/changeUserPassword.png"
        imageAlt="Users"
      />
      {/* measures  */}
<Section
        id="measuresList"
        title="Measures"
        description="This page contains all measures which are in database."
        listing={[
          {
            number: "1",
            text: "This page shows the list of all measures in database, and users can add new measure according to their permissions.",
          },
          
          
        ]}
        imageSrc="/measure.svg"
        imageAlt="Measures"
      />

<Section
        id="addMeasure"
        title="Add New Measure"
        description="This page contains form for add new measure."
        listing={[
          {
            number: "1",
            text: "Click on add measure button then fill the form, all fields are required after filling the required informations you will be able to save those recorded informations in database.",
          },
          
          {
            number: "2",
            text: "Click on save button, and at the time you want to cancel click on cancel button.",
          },
          
        ]}
        imageSrc="/addMeasure.png"
        imageAlt="Measures"
      />
<Section
        id="editMeasure"
        title="Edit Measure"
        description="On this page we will found the form for edit measure."
        listing={[
          {
            number: "1",
            text: "Press on edit icon on the listing of measures then you will get a popup form where you will be able to edit measure.",
          },
          
          {
            number: "2",
            text: "Make change you want to make then save them by clicking on save button, and you can cancel anytime if you want to do it click on cancel button.",
          },
          
        ]}
        imageSrc="/editMeasure.png"
        imageAlt="Measures"
      />
<Section
        id="deleteMeasure"
        title="Delete Measure"
        description="On this page we will found the form for delete measure."
        listing={[
          {
            number: "1",
            text: "Press on delete icon on the list of measures page, then you will get pop up form asking if you want to delete measure.",
          },
          
          {
            number: "2",
            text: "Confirm deletion by clicking on delete button, if you want to cancel the process click on cancel button.",
          },
          
        ]}
        imageSrc="/deleteMeasure.png"
        imageAlt="Measures"
      />
      {/* positions  */}
<Section
        id="positions"
        title="Positions"
        description="This page contains all of the positions where the authenticated user should navitage to this page."
        listing={[
          {
            number: "1",
            text: "Positions page will be accessed with admin, who will be able to delete, add, and edit new positions in system.",
          },
          
        ]}
        imageSrc="/position.png"
        imageAlt="Position"
      />

<Section
        id="addPositions"
        title="Add Position"
        description="This page shows form that contains the fields you have to fill."
        listing={[
          {
            number: "1",
            text: "Add new position form you will be able to fill that form with necessarily informations and save them.",
          },
          
        ]}
        imageSrc="/addPosition.png"
        imageAlt="Position"
      />

<Section
        id="editPositions"
        title="Edit Position"
        description="This page contains will show you the form while you clicked on certain user you will be able to edit the selected information."
        listing={[
          {
            number: "1",
            text: "Positions page will be accessed with admin, who will be able to edit selected positions from system.",
          },
          
        ]}
        imageSrc="/editPosition.png"
        imageAlt="Position"
      />
<Section
        id="deletePosition"
        title="Delete Position"
        description="This page shows form for delete position."
        listing={[
          {
            number: "1",
            text: "Click on icons and choose delete.",
          },
          {
            number: "2",
            text: "Confirm by click on delete button then the position will be deleted, and if you don't want to delete you can cancel by clicking on cancel button.",
          },
          
        ]}
        imageSrc="/deletePosition.png"
        imageAlt="Position"
      />
      {/* targets  */}
<Section
        id="targets"
        title="Target Dashboard"
        description="This page provides an overview of all targets across various measures and hospitals within the system."
        listing={[
          {
            number: "1",
            text: "These are the listing for all targets in database user can navigate through them according to the user's permission.",
          },
          
          
        ]}
        imageSrc="/target.png"
        imageAlt="Target"
      />
<Section
        id="addTarget"
        title="Add Target"
        description="This form is for adding new target on the list."
        listing={[
          {
            number: "1",
            text: "At the time you want to add new target in database you click on the add target button.",
          },
          {
            number: "2",
            text: "You get pop up form where you fill the necessarily informations.",
          },
          {
            number: "3",
            text: "After filling that form you click on save button to keep them on database.",
          },
          {
            number: "4",
            text: "At the time you clicked on the form or button accidentally you can easily cancel by clicking on the cancel button.",
          },
          
          
        ]}
        imageSrc="/addTarget.png"
        imageAlt="Target"
      />

<Section
        id="editTarget"
        title="Edit Target"
        description="The form for editing will be displayed while you clicked on the edit icon inside the target list."
        listing={[
          {
            number: "1",
            text: "To access the form for editing, click on the edit icon within the target list.",
          },
          {
            number: "2",
            text: "After clicking the edit icon, the form for editing will be displayed, allowing you to modify the target's details, by confirming while clicking on save button the edited data will be saved.",
          },
          
          
        ]}
        imageSrc="/editTarget.png"
        imageAlt="Target"
      />

<Section
        id="deleteTarget"
        title="Delete Target"
        description="This pop-up form presents a message for deleting the selected target. You can either confirm deletion or cancel the action."
        listing={[
          {
            number: "1",
            text: "This form is designed for deleting a selected item. To initiate the deletion process, click on the delete icon associated with the target.",
          },
          {
            number: "2",
            text: "After clicking the edit icon, the form for editing will be displayed, allowing you to modify the target's details, by confirming while clicking on save button the edited data will be saved.",
          },
          
          
        ]}
        imageSrc="/deleteTarget.png"
        imageAlt="Target"
      />

      {/* Notifications  */}
<Section
        id="notifications"
        title="Notifications"
        description="This page serves as a central hub for all system notifications. Here, you will find updates and alerts regarding various activities and events taking place within the system."
        listing={[
          {
            number: "1",
            text: "This page displays notifications corresponding to system activities. Stay updated with alerts and updates reflecting events occurring within the system.",
          },
          
          
          
        ]}
        imageSrc="/notification.png"
        imageAlt="notification"
      />
      {/* reports  */}
      <Section
        id="reports"
        title="Reports"
        description="This page compiles all reports, accessible exclusively by administrators for navigation through them."
        listing={[
          {
            number: "1",
            text: "This page shows you the overview for all hospital reports, allowing easy navigation.",
          },
          
          
          
        ]}
        imageSrc="/reports.png"
        imageAlt="Reports"
      />

    </div>
  );
};
const DashboardDocPage = () => {
    return (
        <DashboardContainerDoc content={<DashboardContainerData />} pageTitle={'Documentation'} />
    );
};

export default DashboardDocPage;

