# Cohesive KPI Dashboard

A comprehensive dashboard application for tracking and visualizing Key Performance Indicators (KPIs) for healthcare facilities.

## Live Demo

The application is deployed at: [https://kpi.cohesiveapps.com/](https://kpi.cohesiveapps.com/)

## Features

- **User Management**: Create, edit, and manage user accounts with role-based permissions
- **Hospital Management**: Track and manage multiple hospital facilities
- **KPI Tracking**: Monitor key performance indicators across different hospitals
- **Target Setting**: Set and track targets for various performance measures
- **Data Visualization**: View data through charts, tables, and dashboards
- **Role-Based Access Control**: Different permission levels for administrators, managers, and users
- **Responsive Design**: Works on desktop and mobile devices

## Technical Stack

- **Frontend**: React.js with functional components and hooks
- **State Management**: React Context API and useState/useEffect hooks
- **Styling**: CSS/SCSS with responsive design principles
- **UI Components**: Custom components and Material UI
- **Charts**: MUI X-Charts for data visualization
- **HTTP Client**: Axios for API requests
- **Authentication**: JWT token-based authentication
- **PDF Generation**: jsPDF for exporting reports

## Project Structure

```
src/
├── api/                  # API configuration and endpoints
├── assets/               # Static assets (CSS, images)
│   ├── css/              # Stylesheets
│   │   ├── components/   # Component-specific styles
│   │   ├── dashboard/    # Dashboard styles
│   │   ├── loading/      # Loading indicators styles
│   │   ├── main/         # Global styles
│   │   ├── pages/        # Page-specific styles
│   │   └── ...
├── components/           # Reusable UI components
│   ├── dashboard/        # Dashboard-specific components
│   ├── forms/            # Form components
│   ├── hospitals/        # Hospital-related components
│   ├── loading/          # Loading indicators
│   ├── position/         # User position/role components
│   ├── targets/          # Target-related components
│   └── ...
├── pages/                # Application pages
│   ├── account/          # User account pages
│   ├── auth/             # Authentication pages
│   ├── dashboard/        # Dashboard pages
│   ├── singleHospital/   # Single hospital view
│   ├── target/           # Target management
│   └── ...
└── services/             # Utility services
    ├── formatDate.js     # Date formatting utilities
    ├── protectedRoute.js # Route protection logic
    ├── userPosition.js   # User role/permission utilities
    └── ...
```

## Loading Indicators

The application uses three types of loading indicators:

1. **TableLoading**: For table-specific loading that allows other UI elements to remain functional
2. **LoginLoading**: For full-screen loading during authentication
3. **PageLoading**: For general page loading states

### Using TableLoading

TableLoading is designed to only affect tables while allowing other UI elements to remain functional:

```jsx
<div className="table-container">
  {isLoading && <TableLoading />}
  <table>
    {/* Table content */}
  </table>
</div>
```

This approach ensures that:
- The loading indicator only appears over the table
- Other UI elements remain accessible
- Users can still use buttons, search, and other functionality while data is loading

## Authentication

The application uses JWT token-based authentication:

1. User logs in with credentials
2. Server validates credentials and returns JWT token
3. Token is stored in cookies
4. Token is included in subsequent API requests
5. Protected routes check for valid token

## Role-Based Access Control

The application supports different user roles:

- **Admin**: Full access to all features
- **Super User**: Access to most features except system configuration
- **Manager**: Access to hospital management and reporting
- **User**: Limited access to view data and reports
- **User Editor**: Can manage users but not system settings

## Development Setup

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Start the development server:
   ```
   npm start
   ```
4. The application will be available at `http://localhost:3000`

## Building for Production

```
npm run build
```

This creates a production-ready build in the `build` folder.

## Deployment

The application is deployed to the production environment at [https://kpi.cohesiveapps.com/](https://kpi.cohesiveapps.com/) using CI/CD pipelines.

## Best Practices

- Use TableLoading for data fetching operations that should not block the entire UI
- Use LoginLoading only during authentication processes
- Use PageLoading sparingly, only when the entire page needs to be blocked
- Follow the established component structure for consistency
- Use the API service for all backend communication
- Implement proper error handling for API requests
- Follow the role-based permission checks for UI elements

## Troubleshooting

If you encounter issues with loading indicators:

1. Check that your container has `position: relative`
2. Ensure the TableLoading component is a direct child of the container
3. Verify that you're using the correct loading component for your use case
4. Check for any CSS conflicts that might be affecting the loading overlay

## License

Proprietary - All rights reserved
