.measure-action-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: end;
    align-items: center;
    z-index: 1000;
}

.measure-action-dialog {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    display: flex;
    flex-direction: column;
    gap: 12px;
    

    h2 {
        color: #333;
        font-size: 24px;
        font-weight: 600;
        text-align: center;
    }

    > p {
        color: #666;
        text-align: center;
        font-size: 16px;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.action-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.action-option {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
        border-color: #07AEEF;
        background-color: #f8f9fa;
    }
}

.option-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    &.create-icon {
        background-color: #e8f5e8;
        color: #28a745;
    }

    &.copy-icon {
        background-color: #e3f2fd;
        color: #1976d2;
    }
}

.option-content {
    flex: 1;

    h3 {
        color: #333;
        font-size: 18px;
        font-weight: 600;
    }

    p {
        color: #666;
        font-size: 14px;
    }
}

.dialog-actions {
    display: flex;
    justify-content: center;
    padding-top: 20px;
}

@media (max-width: 600px) {
    .measure-action-dialog {
        padding: 20px;
   
    }
    
    .action-option {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .option-content {
        text-align: center;
    }
}
