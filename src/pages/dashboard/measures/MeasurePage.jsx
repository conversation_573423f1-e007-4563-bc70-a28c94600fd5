import React, { useEffect, useState, useCallback, useRef } from 'react';
import { CleanIcon, Edit02Icon, Delete01Icon, PlusSignIcon, Search01Icon } from 'hugeicons-react';
import { TextInput } from '../../../components/forms/Input';
import api from '../../../api';
import toast from 'react-hot-toast';
import NewMeasureForm from '../../../components/forms/measures/NewMeasureForm';
import StepByStepMeasureForm from '../../../components/forms/measures/StepByStepMeasureForm';
import DashboardContainer from '../../../components/dashboard/DashboardContainer';
import EditMeasureForm from '../../../components/forms/measures/EditMeasure';
import DeleteMeasure from '../../../components/forms/measures/DeleteMeasure';
import MeasureActionDialog from '../../../components/forms/measures/MeasureActionDialog';
import CopyMeasuresModal from '../../../components/forms/measures/CopyMeasuresModal';
import MeasureTooltip from '../../../components/common/MeasureTooltip';
import SortableTableHeader from '../../../components/common/SortableTableHeader';
import FilterableTableHeader from '../../../components/common/FilterableTableHeader';
import '../../../assets/css/pages/measures/measures.css';
import TableLoading from '../../../components/loading/TableLoading';
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { hasRole } from '../../../services/userPosition';
import { PrimaryButton, SecondaryButton } from '../../../components/forms/buttons';

const MeasuresTable = ({ measures = ({}), onEditClick, onDeleteClick, onReorder, onSort, currentSort, onFilter, filters }) => {
    const handleDragEnd = (result) => {
        const { source, destination } = result;

        if (!destination || source.index === destination.index) return;

        const updatedMeasures = Array.from(measures);
        const [movedItem] = updatedMeasures.splice(source.index, 1);
        updatedMeasures.splice(destination.index, 0, movedItem);

        onReorder(updatedMeasures);
    };

    const [selectedMeasures, setSelectedMeasures] = useState([]);

    const handleSelectMeasure = (measureId) => {
        const index = selectedMeasures.indexOf(measureId);
        if (index === -1) {
            setSelectedMeasures([...selectedMeasures, measureId]);
        } else {
            setSelectedMeasures(selectedMeasures.filter((id) => id !== measureId));
        }
    };

    return !measures || !Array.isArray(measures) || measures.length < 1 ? 'Data not Found...' : (
        <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="measuresTable">
                {(provided) => (
                    <table {...provided.droppableProps} ref={provided.innerRef}>
                        <thead>
                            <tr>
                                <FilterableTableHeader
                                    label="Measure ID"
                                    sortKey="synchronous_id"
                                    currentSort={currentSort || { key: '', direction: 'asc' }}
                                    onSort={onSort || (() => {})}
                                    onFilter={onFilter || (() => {})}
                                    filterValue={filters?.synchronous_id || ''}
                                    filterType="text"
                                />
                                <FilterableTableHeader
                                    label="Measure Name"
                                    sortKey="name"
                                    currentSort={currentSort || { key: '', direction: 'asc' }}
                                    onSort={onSort || (() => {})}
                                    onFilter={onFilter || (() => {})}
                                    filterValue={filters?.name || ''}
                                    filterType="text"
                                />
                                <FilterableTableHeader
                                    label="Category Name"
                                    sortKey="category_name"
                                    currentSort={currentSort || { key: '', direction: 'asc' }}
                                    onSort={onSort || (() => {})}
                                    onFilter={onFilter || (() => {})}
                                    filterValue={filters?.category_name || ''}
                                    filterType="text"
                                />
                                <FilterableTableHeader
                                    label="Group Name"
                                    sortKey="group"
                                    currentSort={currentSort || { key: '', direction: 'asc' }}
                                    onSort={onSort || (() => {})}
                                    onFilter={onFilter || (() => {})}
                                    filterValue={filters?.group || ''}
                                    filterType="text"
                                />
                                {
                                    hasRole(["Admin","Super User"])?
                                    <th>Actions</th>
                                    :
                                    <></>
                                }

                            </tr>
                        </thead>
                        <tbody>
                            {measures.map((measure, index) => (
                                <Draggable
                                    key={measure.id?.toString() || `fallback-key-${index}`}
                                    draggableId={measure.id?.toString() || `fallback-id-${index}`}
                                    index={index}
                                >

                                    {(provided) => (
                                        <tr
                                            ref={provided.innerRef}
                                            {...provided.draggableProps}
                                            {...provided.dragHandleProps}
                                        >
                                            <td>{measure.synchronous_id }</td>
                                            <td>
                                                <MeasureTooltip
                                                    measureName={measure.name}
                                                    description={measure.description || measure.measure_description}
                                                />
                                            </td>
                                            <td>{measure.category_name || measure.category}</td>
                                            <td>{measure.group}</td>
                                            {
                                            hasRole(["Admin","Super User"])?
                                            <td>
                                                <div className="action-buttons">
                                                    <Edit02Icon
                                                    size={34}
                                                    color='green'

                                                        className="edit-icon"
                                                        onClick={() => onEditClick(measure.measure_id)}
                                                    />
                                                    <Delete01Icon
                                                    size={34}
                                                    color='red'
                                                        className="delete-icon"
                                                        onClick={() => onDeleteClick(measure.measure_id)}
                                                    />
                                                </div>
                                            </td>
                                                :
                                                <></>

                                            }

                                        </tr>
                                    )}
                                </Draggable>
                            ))}
                            {provided.placeholder}
                        </tbody>
                    </table>
                )}
            </Droppable>
        </DragDropContext>
    );
};

// Removed old LoadingOverlay in favor of TableLoading component

const MeasurePageContent = () => {
    const [measuresData, setMeasuresData] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [sortedData, setSortedData] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [errorFetching, setErrorFetching] = useState(false);
    const [showCopyMeasuresModal, setShowCopyMeasuresModal] = useState(false);
    const [showActionDialog, setShowActionDialog] = useState(false);
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    const [currentDateYear] = useState(new Date().getFullYear());
    const [prevYear] = useState(new Date().getFullYear() - 1);
    const [prevYearMinusTwo] = useState(new Date().getFullYear() - 2);
    const [prevYearMinusThree] = useState(new Date().getFullYear() - 3);
    const [isOverlayVisible, setIsOverlayVisible] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [currentSort, setCurrentSort] = useState({ key: '', direction: 'asc' });
    const [filters, setFilters] = useState({});

    const [showNewMeasureForm, setShowNewMeasureForm] = useState(false);
    const [showEditMeasureForm, setShowEditMeasureForm] = useState(false);
    const [showDeleteMeasurePopup, setShowDeleteMeasurePopup] = useState(false);
    const [selectedMeasureId, setSelectedMeasureId] = useState(null);
    const [selectedMeasureForDeletion, setSelectedMeasureForDeletion] = useState(null);
    const [measureData, setMeasureData] = useState(null);

    const debounceTimeoutRef = useRef(null);

    const fetchMeasures = useCallback(async (year) => {
        setIsOverlayVisible(true);
        setIsLoading(true);
        try {
            const response = await api.get(`/measures?measure_year=${year}`);
            if (response.status === 200) {
                setMeasuresData(response.data);
                setFilteredData(response.data);
                setSortedData(response.data);
            }
        } catch (error) {
            toast.error(error.response?.data?.error || 'Unknown error while fetching measures');
            setErrorFetching(true);
        } finally {
            setIsLoading(false);
            setIsOverlayVisible(false);
        }
    }, []);

    // Handle year change
    const handleDurationYearChange = (event) => {
        const newYear = event.target.value;
        setSelectedYear(newYear);
    };

    // Fetch measures when year changes
    useEffect(() => {
        fetchMeasures(selectedYear);
    }, [selectedYear, fetchMeasures]);


    useEffect(() => {
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }

        debounceTimeoutRef.current = setTimeout(() => {
            // Apply search and filters together
            let filteredData = measuresData;

            // Apply search query
            if (searchQuery) {
                filteredData = filteredData.filter(measure =>
                    measure.name.toLowerCase().includes(searchQuery.toLowerCase())
                );
            }

            // Apply column filters
            Object.keys(filters).forEach(key => {
                const filterValue = filters[key];
                if (filterValue) {
                    filteredData = filteredData.filter(measure => {
                        const measureValue = measure[key] || '';
                        return measureValue.toString().toLowerCase().includes(filterValue.toLowerCase());
                    });
                }
            });

            setFilteredData(filteredData);
            setSortedData(filteredData);
        }, 300);

        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
            }
        };
    }, [searchQuery, measuresData, filters]);

    const handleClearSearch = () => {
        setSearchQuery('');
        setFilters({});
        setFilteredData(measuresData);
        setSortedData(measuresData);
    };

    const filterByRecent = (condition) => {
        let dataToSort = [...filteredData];

        if (condition === 'recent') {
            dataToSort.sort((a, b) => new Date(b.date_created) - new Date(a.date_created));
        } else if (condition === 'oldest') {
            dataToSort.sort((a, b) => new Date(a.date_created) - new Date(b.date_created));
        }

        setSortedData(dataToSort);
    };

    const handleEditMeasure = (measureId) => {
        const measureData = measuresData.find(measure => measure.measure_id === measureId);
        setSelectedMeasureId(measureId);
        setMeasureData(measureData);
        setShowEditMeasureForm(true);
    };

    const handleDeleteClick = (measureId) => {
        setSelectedMeasureForDeletion(measureId);
        setShowDeleteMeasurePopup(true);
    };

    const handleCloseEditForm = () => {
        setShowEditMeasureForm(false);
        setSelectedMeasureId(null);
    };

    const handleMeasureAdded = (newMeasure) => {
        setMeasuresData(prevMeasures => [...prevMeasures, newMeasure]);
        // try {
        //     localStorage.setItem(`measuresOrder-${selectedYear}`, JSON.stringify(measuresData));
        // } catch (error) {
        //     console.error('Error saving order to local storage:', error);

        // }
        setShowNewMeasureForm(false);
    };

    const handleReorder = (updatedMeasures) => {
        setMeasuresData(updatedMeasures);
        // try {
        //     localStorage.setItem(`measuresOrder-${selectedYear}`, JSON.stringify(updatedMeasures));
        // } catch (error) {
        //     console.error('Error saving order to local storage:', error);

        // }
    };

    const handleNewMeasureClick = () => {
        setShowActionDialog(true);
    };

    const handleCreateNewMeasure = () => {
        setShowActionDialog(false);
        setShowNewMeasureForm(true);
    };

    const handleCopyMeasuresFromDialog = () => {
        setShowActionDialog(false);
        setShowCopyMeasuresModal(true);
    };

    const handleSort = (sortConfig) => {
        setCurrentSort(sortConfig);

        const sortedData = [...filteredData].sort((a, b) => {
            const aValue = a[sortConfig.key] || '';
            const bValue = b[sortConfig.key] || '';

            // Handle different data types
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
                return sortConfig.direction === 'asc' ? comparison : -comparison;
            }

            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
            }

            // Default string comparison
            const comparison = String(aValue).localeCompare(String(bValue));
            return sortConfig.direction === 'asc' ? comparison : -comparison;
        });

        setSortedData(sortedData);
    };

    const handleFilter = (column, value) => {
        const newFilters = { ...filters, [column]: value };
        setFilters(newFilters);

        // Apply filters to the data
        let filteredData = measuresData;

        // Apply search query first
        if (searchQuery) {
            filteredData = filteredData.filter(measure =>
                measure.name.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }

        // Apply column filters
        Object.keys(newFilters).forEach(key => {
            const filterValue = newFilters[key];
            if (filterValue) {
                filteredData = filteredData.filter(measure => {
                    const measureValue = measure[key] || '';
                    return measureValue.toString().toLowerCase().includes(filterValue.toLowerCase());
                });
            }
        });

        setFilteredData(filteredData);

        // Apply current sort to filtered data
        if (currentSort.key) {
            const sortedData = [...filteredData].sort((a, b) => {
                const aValue = a[currentSort.key] || '';
                const bValue = b[currentSort.key] || '';

                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
                    return currentSort.direction === 'asc' ? comparison : -comparison;
                }

                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return currentSort.direction === 'asc' ? aValue - bValue : bValue - aValue;
                }

                const comparison = String(aValue).localeCompare(String(bValue));
                return currentSort.direction === 'asc' ? comparison : -comparison;
            });
            setSortedData(sortedData);
        } else {
            setSortedData(filteredData);
        }
    };

    return errorFetching ? 'Error while getting measures' : (
        <div className='measures-page'>
            <StepByStepMeasureForm
                isOpen={showNewMeasureForm}
                onClose={() => setShowNewMeasureForm(false)}
                onMeasureAdded={handleMeasureAdded}
            />
            {showEditMeasureForm && selectedMeasureId && (
                <div className="popup edit-measure-form-popup">
                    <div className="popup-content">
                        <EditMeasureForm measureId={selectedMeasureId} measureData={measureData} onClose={handleCloseEditForm} year={selectedYear} />
                    </div>
                </div>
            )}
            {showDeleteMeasurePopup && selectedMeasureForDeletion && (
                <DeleteMeasure
                    measureId={selectedMeasureForDeletion}
                    synchronousId={measuresData.find(measure => measure.measure_id === selectedMeasureForDeletion)?.synchronous_id || ''}
                    isOpen={showDeleteMeasurePopup}
                    onClose={() => setShowDeleteMeasurePopup(false)}
                    removeMeasure={(measureId) => {
                        setMeasuresData(measuresData.filter(measure => measure.id !== measureId));
                        setShowDeleteMeasurePopup(false);
                    }}
                />
            )}

            <div className="search-filter">
                <div className="filters">
                    <TextInput
                        iconClass={<Search01Icon />}
                        type='search'
                        placeholder='Search by measure name'
                        value={searchQuery}
                        setValue={(value) => setSearchQuery(value)}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                            }
                        }}
                    />
                    {searchQuery && (
                        <SecondaryButton
                            iconClass={<CleanIcon />}
                            buttonText='Reset'
                            onClick={handleClearSearch}
                        />
                    )}
                    <Select
                        name="duration"
                        id="duration"
                        value={selectedYear}
                        onChange={handleDurationYearChange}
                        className='year-select'
                    >
                        <MenuItem value={currentDateYear}>{currentDateYear}</MenuItem>
                        <MenuItem value={prevYear}>{prevYear}</MenuItem>
                        <MenuItem value={prevYearMinusTwo}>{prevYearMinusTwo}</MenuItem>
                        <MenuItem value={prevYearMinusThree}>{prevYearMinusThree}</MenuItem>
                    </Select>
                    <div className="action">
                        {hasRole(["Admin", "Super User"]) && (
                            <PrimaryButton
                                iconClass={<PlusSignIcon />}
                                buttonText='New Measure'
                                onClick={handleNewMeasureClick}
                            />
                        )}
                    </div>
                </div>
            </div>

            <MeasureActionDialog
                isOpen={showActionDialog}
                onClose={() => setShowActionDialog(false)}
                onCreateNew={handleCreateNewMeasure}
                onCopyMeasures={handleCopyMeasuresFromDialog}
            />

            {showCopyMeasuresModal && (
                <CopyMeasuresModal
                    open={showCopyMeasuresModal}
                    onClose={() => setShowCopyMeasuresModal(false)}
                    measures={measuresData}
                    currentYear={selectedYear}
                />
            )}

            <div className="measures table-container">
                {isOverlayVisible && <TableLoading />}
                <MeasuresTable
                    measures={sortedData}
                    onEditClick={handleEditMeasure}
                    onDeleteClick={handleDeleteClick}
                    onReorder={handleReorder}
                    onSort={handleSort}
                    currentSort={currentSort}
                    onFilter={handleFilter}
                    filters={filters}
                />
            </div>
        </div>
    );
};

const MeasurePage = () => {
    return (
        <DashboardContainer content={<MeasurePageContent />} pageTitle={'Measure'} />
    );
};

export default MeasurePage;
