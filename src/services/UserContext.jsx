import React, { createContext, useState, useEffect, useContext } from 'react';
import api, { API_URL } from '../api'; // Adjust the import as necessary

const UserContext = createContext();

export const UserProvider = ({ children }) => {
    const [groups, setGroups] = useState([]); // State to hold user groups
    const [loading, setLoading] = useState(true); // Loading state

    useEffect(() => {
        const fetchUserGroups = async () => {
            try {
const response = await api.get(`${API_URL}/user/list_users/`); // API call to fetch user groups
                if (response.status === 200) {
                    setGroups(response.data.groups); // Assuming groups are in response.data.groups
                } else {
                    setGroups([]); // Set to empty if the request fails
                }
            } catch (error) {
                console.error(error);
                setGroups([]); // Handle error case
            } finally {
                setLoading(false); // Set loading to false after fetch
            }
        };

        fetchUserGroups();
    }, []); // Run once on mount

    return (
        <UserContext.Provider value={{ groups, loading }}>
            {children} {/* Provide the context to child components */}
        </UserContext.Provider>
    );
};

// Custom hook to use the UserContext
export const useUserGroups = () => {
    const context = useContext(UserContext);
    if (!context) {
        throw new Error("useUserGroups must be used within a UserProvider");
    }
    return context.groups; // Return the groups
};

// Optional: Custom hook to check if user has a specific group
export const useHasGroup = (group) => {
    const groups = useUserGroups();
    return groups.includes(group); // Returns true or false based on the check
};
