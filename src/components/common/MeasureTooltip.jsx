import React, { useState } from 'react';
import { InformationCircleIcon } from 'hugeicons-react';
import '../../assets/css/components/common/measure-tooltip.css';

const MeasureTooltip = ({ measureName, description, children }) => {
    const [isVisible, setIsVisible] = useState(false);

    return (
        <div 
            className="measure-tooltip-container"
            onMouseEnter={() => setIsVisible(true)}
            onMouseLeave={() => setIsVisible(false)}
        >
            {children || (
                <span className="measure-name-with-tooltip">
                    {measureName}
                    <InformationCircleIcon size={16} className="info-icon" />
                </span>
            )}
            {isVisible && description && (
                <div className="measure-tooltip">
                    <div className="tooltip-header">
                        <strong>Measure Description</strong>
                    </div>
                    <div className="tooltip-content">
                        {description}
                    </div>
                    <div className="tooltip-arrow"></div>
                </div>
            )}
        </div>
    );
};

export default MeasureTooltip;
