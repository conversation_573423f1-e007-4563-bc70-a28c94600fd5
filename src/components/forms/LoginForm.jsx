import React, { useState } from 'react';
import axios from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';
import { useMsal } from '@azure/msal-react';
import { loginRequest } from './msalConfig';
import { TextInput } from './Input';
import { PrimaryButton } from './buttons';
import { Link } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import { API_URL } from '../../api';
import LoginLoading from '../../components/loading/LoginLoading';


export const fetchUserPermissions = async (accessToken) => {
    try {
        const response = await axios.get(`${API_URL}/user/profile/`, {
            headers: { Authorization: `Bearer ${accessToken}` },
        });
        const user = response.data;

        localStorage.setItem('position', user.position);
        localStorage.setItem('hospital', user.hospital);
        localStorage.setItem('hospital_id', user.hospital_id);

        return user;
    } catch (error) {
        console.error('Error fetching user permissions:', error);
        throw error;
    }
};

const LoginForm = () => {
    const navigate = useNavigate();
    const { instance } = useMsal();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isAuthenticating, setIsAuthenticating] = useState(false);

    const handleLogin = async (e) => {
        e.preventDefault();
        if (!email || !password) {
            toast.error('Please fill in all fields.');
            return;
        }

        setIsLoading(true);
        setIsAuthenticating(true);
        try {
            const credentials = {
                email,
                password,
                auth_type: "email_password"
            };

            const response = await axios.post(`${API_URL}/accounts/token/`, credentials);
            const { access_token, refresh_token } = response.data;

            // Set tokens in cookies
            Cookies.set('accessToken', access_token);
            Cookies.set('refreshToken', refresh_token);

            // Fetch user data
            await fetchUserPermissions(access_token);

            toast.success('Successfully logged in');

            // Use navigate instead of window.location
            navigate('/');
        } catch (error) {
            console.error('Login failed:', error);
            const errorMessage = error.response?.data?.error || 'Login failed. Please try again.';
            toast.error(errorMessage);
            setIsAuthenticating(false);
        } finally {
            setIsLoading(false);
        }
    };

    const handleMicrosoftLogin = async () => {
        try {
            const loginResponse = await instance.loginPopup(loginRequest);
            const microsoftAccessToken = loginResponse.accessToken;
            const email = loginResponse.account.username

            const credentials = { email, access_token: microsoftAccessToken, auth_type: "microsoft" };

            try {
                const response = await axios.post(`${API_URL}/accounts/token/`, credentials);
                const { access_token, refresh_token } = response.data;

                if (response.status === 200) {
                    Cookies.set('accessToken', access_token);
                    Cookies.set('refreshToken', refresh_token);
                    await fetchUserPermissions(Cookies.get('accessToken'));
                    toast.success('Successfully logged in with Microsoft');
                }

            } catch (error) {
                toast.error(error.response.data.error);
            }


        } catch (error) {
            console.error('Microsoft login failed:', error);
            toast.error(' We are having trouble accessing your Microsoft Account...');
        }
    };


    return (
        <>
            {isAuthenticating && <LoginLoading />}
            <div className="form">
                <form onSubmit={handleLogin}>
                    <TextInput
                        type="email"
                        name="email"
                        value={email}
                        setValue={setEmail}
                        placeholder="Enter your email"
                    />
                    <TextInput
                        type="password"
                        name="password"
                        value={password}
                        setValue={setPassword}
                        placeholder="Enter your password"
                    />
                    <PrimaryButton
                        buttonText="Login"
                        processingText="Logging in"
                        onClick={handleLogin}
                        isLoading={isLoading}
                    />

                    {/* Microsoft Login Button */}
                    {/* <div className="microsoft-login">
                    <button className="primary-button" onClick={handleMicrosoftLogin}>
                        Sign in with Microsoft
                    </button>
                </div> */}
                </form>


                {/* Forgot Password Link */}
                <span>
                    Forgot password? <Link to="/forgot-password/" className="form-link">Click here</Link>
                </span>
                {/* <div className='documentation'>
                <Link to={"/documentation/"}>Documentation</Link>
                </div> */}

            </div>
        </>
    );
};

export default LoginForm;
