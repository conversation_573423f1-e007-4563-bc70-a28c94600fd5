.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.popup-overlay .add-target-popup {
  width: 50%;
  background-color: white;
  padding: 24px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
}
.popup-overlay .add-target-popup .title {
  color: black;
}
.popup-overlay .add-target-popup .dateInput {
  display: flex;
  gap: 10px;
}
.popup-overlay .add-target-popup .action-btn {
  display: flex;
  gap: 10px;
}
.popup-overlay .add-target-popup .label-dates {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.target-page .main-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.target-page .main-container .targets {
  background-color: white;
  padding: 24px;
  border-radius: 0.5rem;
  overflow: auto;
}
.target-page .main-container .targets .expanded-row {
  display: flex;
  gap: 24px;
}
.target-page .main-container .targets .expanded-row .dates {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.target-page .main-container .targets .expanded-row .dates .icon-date {
  display: flex;
  align-items: center;
  gap: 4px;
}
.target-page .main-container .buttons-search {
  display: flex;
  justify-content: space-between;
  background-color: white;
  padding: 24px;
  align-items: center;
  border-radius: 0.5rem;
}
.target-page .main-container .buttons-search .search-input {
  width: 50%;
}
.target-page .main-container .search-button {
  width: 100%;
  display: flex;
  gap: 12px;
}

@media screen and (max-width: 600px) {
  .target-page .main-container .search-button {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  .target-page .main-container .buttons-search {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .target-page .main-container .buttons-search .search-input {
    width: 100%;
  }
}/*# sourceMappingURL=targets.css.map */