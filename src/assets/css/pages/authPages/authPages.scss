@import '../../variables';

.auth-pages {
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;

    .page-content {
        width: 500px;
        display: grid;
        text-align: center;
        gap: 1rem;

        .logo {
            max-width: 72px;
            margin: auto !important;
        }
        .documentation{
            background-color: #07AEEF;
            padding: 12px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            cursor: pointer;
        }

       
    }

}
.sidebar-doc{
    background-color: #07aeef;
    color: #fff;
    height: 100vh;
    min-width: 252px;
    position: sticky;
    width:20%;
    overflow-y: auto;

    .contents{
        display: flex;
        flex-direction: column;
        gap: 24px;
        padding: 24px;

        .dropdown{
            display: flex;
            flex-direction: column;
            gap: 12px;
            a{
                cursor: pointer;
            }
            .login-content{
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
        }

        
    }
}
.section {
    background-color: whitesmoke;
    border-radius: 12px;
    margin-right: 24px;
    padding: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;

    .text {
        width: 50%;
        display: flex;
        flex-direction: column;
        gap: 48px;

        .listing {
            display: flex;
            flex-direction: column;
            gap: 24px;

            .number-text {
                display: flex;
                gap: 10px;

                .number {
                    padding: 10px;
                    background-color: #07AEEF;
                    border-radius: 4px;
                    width: 24px;
                    height: 24px;
                    color: white;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                   
                }
                .email{
                    color: #07AEEF;
                    font-weight: bolder;
                }
            }
        }
    }

    .image {
        width: 50%;

        img {
            width: 100%;
            object-fit: cover;
        }
    }
}


@media screen and (max-width: 560px) {
    .auth-pages {
        .page-content {
            width: 90vw;
        }
    }
}