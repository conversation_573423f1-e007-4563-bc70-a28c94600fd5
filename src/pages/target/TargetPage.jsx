import React, { useEffect, useState } from 'react';
import { PlusSignIcon } from 'hugeicons-react';
import { PrimaryButton } from '../../components/forms/buttons';
import { Delete01Icon, PencilEdit02Icon } from 'hugeicons-react';
import AddTargetPopup from '../../components/targets/AddTargetPopup';
import DeleteTargetPopup from '../../components/targets/DeleteTargetPopup';
import EditTarget from '../../components/targets/EditTarget';
import DashboardContainer from '../../components/dashboard/DashboardContainer';
import '../../assets/css/targets/targets.css';
import api, { API_URL } from '../../api';
import FormattedDate from '../../services/formatDate';
import { hasRole } from '../../services/userPosition';
import TableLoading from '../../components/loading/TableLoading';
import SearchAndFilters from '../../components/common/SearchAndFilters';
import { FilterSelect } from '../../components/common/StandardFilters';
// Removed old LoadingOverlay in favor of TableLoading component

function isValidSearch(query) {
  const trimmedQuery = query.trim();

  if (trimmedQuery.length > 2) {
    return true;
  }
  return false;
}
// Main Content Component
const TargetPageContent = () => {
  const [targets, setTargets] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isOverlayVisible, setIsOverlayVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isEditPopupOpen, setIsEditPopupOpen] = useState(false);
  const [isDeletePopupOpen, setIsDeletePopupOpen] = useState(false);
  const [selectedTarget, setSelectedTarget] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedValueCondition, setSelectedValueCondition] = useState('');


  const handleTargetAdded = (newTarget) => {
    setTargets((prevTargets) => [...prevTargets, newTarget]);
    console.log('Targets after addition:', [...targets, newTarget]);
  };

  const handleTargetUpdated = (updatedTarget) => {
    setTargets(prevTargets => prevTargets.map(target =>
      target.id === updatedTarget.id ? updatedTarget : target
    ));
  };

  const handleTargetDeleted = (targetId) => {
    setTargets((prevTargets) => Array.isArray(prevTargets) ? prevTargets.filter((target) => target.id !== targetId) : []);
  };

  // Toggle Add Popup
  const handleTogglePopup = () => {
    setIsPopupOpen(!isPopupOpen);
  };

  // Submit Add Popup
  const handlePopupSubmit = (e) => {
    e.preventDefault();
    handleClosePopup();
  };

  // Open Edit Popup
  const handleEditClick = (target) => {
    setSelectedTarget(target);
    setIsEditPopupOpen(true);
  };

  // Handle Search Action
  const handleSearchAction = () => {
    if (searchQuery.trim()) {
      setIsSearching(true);
    }
  };

  // Open Delete Popup
  const handleDeleteClick = (target) => {
    setSelectedTarget(target.target_id);
    setIsDeletePopupOpen(true);
  };

  // Submit Delete Popup
  const handleDeleteSubmit = (id) => {
    setTargets(Array.isArray(targets) ? targets.filter(t => t.target_id !== id) : []);
    handleClosePopup();
  };

  // Close Popups
  const handleClosePopup = () => {
    setIsPopupOpen(false);
    setIsEditPopupOpen(false);
    setIsDeletePopupOpen(false);
  };

  // Handle Search Input Change
  const handleSearchChange = (value) => {
    setSearchQuery(value);
    if (!value.trim()) {
      setIsSearching(false);
    }
  };

  // Reset all filters
  const handleResetFilters = () => {
    setSearchQuery('');
    setSelectedYear(new Date().getFullYear());
    setSelectedCategory('');
    setSelectedValueCondition('');
    setIsSearching(false);
  };


  useEffect(() => {
    const fetchTargets = async () => {
      setIsLoading(true);
      setIsOverlayVisible(true);

      try {
        // Build query parameters
        const params = {
          year: selectedYear
        };

        if (selectedCategory) {
          params.measure_category = selectedCategory;
        }

        if (selectedValueCondition) {
          params.value_condition = selectedValueCondition;
        }

        const response = await api.get(`${API_URL}/targets/`, { params });

        if (response.status === 200) {
          console.log('Targets data:', response.data);

          // Handle different API response formats
          let targetsData = [];
          if (response.data && response.data.results && Array.isArray(response.data.results)) {
            targetsData = response.data.results;
          } else if (Array.isArray(response.data)) {
            targetsData = response.data;
          }

          setTargets(targetsData);
        } else {
          console.log('Error: Response status not 200');
          setTargets([]);
        }
      } catch (error) {
        console.log('Error fetching targets:', error);
        setTargets([]);
      } finally {
        setIsLoading(false);
        setIsOverlayVisible(false);
      }
    };

    fetchTargets();
  }, [selectedYear, selectedCategory, selectedValueCondition]);

  // Filtered Targets Based on Search Query
  const filteredTargets = Array.isArray(targets) ? targets.filter(target => {
    const { measure, target_id, value, date_created, starting_date, end_date } = target;

    // Convert to string only if the value exists and is a string
    const lowerCaseMeasure = measure && typeof measure === 'string' ? measure.toLowerCase() : '';
    const lowerCaseTargetId = target_id ? target_id.toString().toLowerCase() : '';
    const lowerCaseValue = value ? value.toString().toLowerCase() : '';
    const lowerCaseDateCreated = date_created && typeof date_created === 'string' ? date_created.toLowerCase() : '';
    const lowerCaseStartingDate = starting_date && typeof starting_date === 'string' ? starting_date.toLowerCase() : '';
    const lowerCaseEndingDate = end_date && typeof end_date === 'string' ? end_date.toLowerCase() : '';
    const lowerCaseQuery = searchQuery.toLowerCase();

    // Check if the search query matches any of the fields
    return (
      lowerCaseMeasure.includes(lowerCaseQuery) ||
      lowerCaseTargetId.includes(lowerCaseQuery) ||
      lowerCaseValue.includes(lowerCaseQuery) ||
      lowerCaseDateCreated.includes(lowerCaseQuery) ||
      lowerCaseStartingDate.includes(lowerCaseQuery) ||
      lowerCaseEndingDate.includes(lowerCaseQuery)
    );
  }) : [];

  return (
    <div className='target-page dashboard-page'>
      <div className="main-container">
        <SearchAndFilters
          searchValue={searchQuery}
          onSearchChange={handleSearchChange}
          onSearch={handleSearchAction}
          searchPlaceholder="Search by measure name"
          isSearching={isSearching}
          onResetFilters={handleResetFilters}
          showResetFilters={searchQuery || selectedCategory || selectedValueCondition || selectedYear !== new Date().getFullYear()}
          layout="inline"
        >
          <FilterSelect
            label="Year"
            value={selectedYear}
            onChange={setSelectedYear}
            options={[
              { value: new Date().getFullYear(), label: new Date().getFullYear() },
              { value: new Date().getFullYear() - 1, label: new Date().getFullYear() - 1 },
              { value: new Date().getFullYear() - 2, label: new Date().getFullYear() - 2 }
            ]}
          />
          <FilterSelect
            label="Category"
            value={selectedCategory}
            onChange={setSelectedCategory}
            options={[
              'CENSUS - VOLUME & UTILIZATION',
              'QUALITY',
              'FINANCIAL',
              'PATIENT SATISFACTION',
              'EMPLOYEE SATISFACTION'
            ]}
            placeholder="All Categories"
          />
          <FilterSelect
            label="Value Condition"
            value={selectedValueCondition}
            onChange={setSelectedValueCondition}
            options={[
              'Lower is Better',
              'Higher is Better',
              'Target Range'
            ]}
            placeholder="All Conditions"
          />
        </SearchAndFilters>

        <div className='buttons-search'>
          {
            hasRole(["Admin","Super User"]) ?
            <PrimaryButton
            isLoading={false}
            onClick={handleTogglePopup}
            processingText={'Submitting'}
            iconClass={<PlusSignIcon />}
            buttonText={'Add Target'}
          />
            :
            <></>
          }


          {isPopupOpen && (
            <AddTargetPopup
              onClose={handleClosePopup}
              onSubmit={handlePopupSubmit}
              onTargetAdded={handleTargetAdded}
            />
          )}
        </div>



        {filteredTargets.length > 0 ? (
          <div className='targets table-container'>
            {isOverlayVisible && <TableLoading />}
            <table>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Measure Name</th>
                  <th>Target</th>
                  <th>Start Date</th>
                  <th>End Date</th>
                  {hasRole(["Admin","Super User"]) && <th>Action</th>}
                </tr>
              </thead>
              <tbody>
                {isEditPopupOpen && selectedTarget && (
                  <EditTarget
                    targetData={selectedTarget}
                    onClose={handleClosePopup}
                    // onSubmit={handleEditSubmit}
                    onSubmit={handleTargetUpdated}
                  />
                )}
                {isDeletePopupOpen && (
                  <DeleteTargetPopup
                    targetId={selectedTarget}
                    onClose={handleClosePopup}
                    onDelete={handleDeleteSubmit}
                    deleteTarget={handleTargetDeleted}
                  />
                )}

                {filteredTargets.map((target) => (
                  <React.Fragment key={target.target_id}>
                    <tr>
                      <td>{target.target_id}</td>
                      <td>
                        {target.measure
                          ? target.measure.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
                          : 'N/A'}
                      </td>
                      <td>{(target.value || target.value == 0) ? target.value : 0}</td>
                      {/* <td><FormattedDate dateString={target.starting_date || 'N/A'} /></td>
                      <td><FormattedDate dateString={target.end_date || 'N/A'} /></td> */}
                      <td>{target.starting_date}</td>
                      <td>{target.end_date}</td>
                      {hasRole(["Admin","Super User"]) && (
                        <td>
                          <div className='action-buttons'>
                            <PencilEdit02Icon
                            size={32}
                              className="edit-icon"
                              onClick={() => handleEditClick(target)}
                            />
                            <Delete01Icon
                            size={32}
                              className="delete-icon"
                              onClick={() => handleDeleteClick(target)}
                            />
                          </div>
                        </td>
                      )}
                    </tr>
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p>No data found...</p>
        )}
      </div>
    </div>
  );
};

const TargetPage = () => {
  return (
    <DashboardContainer content={<TargetPageContent />} pageTitle={'Targets'} />
  );
};

export default TargetPage;