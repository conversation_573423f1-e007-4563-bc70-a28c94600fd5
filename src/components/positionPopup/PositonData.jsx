import React from "react";
import "../../assets/css/popups/options.css";
import {
  Delete01Icon,
  PencilEdit02Icon,
  ArrowDown01Icon,
} from "hugeicons-react";

const PositonData = () => {
  return (
    <div class="position-data" id="popup-overlay">
      <div className="action-button">
        <div className="edit">
          <p>
            <PencilEdit02Icon />
            Edit
          </p>
        </div>
        <div className="delete">
          <p>
            {" "}
            <Delete01Icon />
            Delete
          </p>
        </div>
      </div>
    </div>
  );
};

export default PositonData;
