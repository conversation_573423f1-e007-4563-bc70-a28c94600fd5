@import '../variables.scss';

.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;

    .add-target-popup {
        width: 50%;
        background-color: white;
        padding: 24px;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        overflow-y: auto;

        .title {
            color: black;
        }

        .dateInput {
            display: flex;
            gap: 10px;


        }

        .action-btn {
            display: flex;
            gap: 10px;
        }

.label-dates{
display: flex;
flex-direction: column;
gap: 10px;
}

    }
}

.target-page {

    .main-container {
        display: flex;
        flex-direction: column;
        gap: 24px;
        
        .targets {

            background-color: white;
padding: 24px;
border-radius: 0.5rem;
overflow: auto;
            .expanded-row {
                display: flex;
                gap: 24px;

                .dates {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;

                    .icon-date {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    }
                }
            }
        }
        .buttons-search{
            display: flex;
            justify-content: space-between;
            background-color: white;
            padding: 24px;
align-items: center;
        border-radius: 0.5rem;
            .search-input {
                width: 50%;
            }
        }

        .search-button {
            width: 100%;
            display: flex;
            gap: 12px;
        }

}

}

@media screen and (max-width:600px) {

    .target-page {

        .main-container {
            .search-button {
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 12px;
            }
            .buttons-search{
                display: flex;
                flex-direction: column;
                gap: 10px;

                .search-input{
                    width: 100%;
                }
               } 
        }
    }
  
}