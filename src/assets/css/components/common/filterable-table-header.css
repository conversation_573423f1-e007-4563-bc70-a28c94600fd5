.filterable-header {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
    position: relative;
    padding: 0 !important;
    vertical-align: top;
}

.filterable-header:hover {
    background-color: #f8fafc;
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
    min-width: 120px;
}

.header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    cursor: pointer;
}

.header-label {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
    flex: 1;
}

.header-icons {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
}

.sort-icons {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.sort-icon {
    transition: all 0.2s ease;
    display: block;
}

.sort-icon.inactive {
    color: #d1d5db;
    opacity: 0.5;
}

.sort-icon.active {
    color: #3b82f6;
    opacity: 1;
}

.filter-icon {
    transition: all 0.2s ease;
    color: #9ca3af;
    cursor: pointer;
}

.filter-icon:hover {
    color: #6b7280;
}

.filter-icon.active {
    color: #3b82f6;
}

.filterable-header:hover .sort-icon.inactive {
    color: #9ca3af;
    opacity: 0.8;
}

.filterable-header:hover .filter-icon {
    color: #6b7280;
}

/* Filter inputs */
.filter-input,
.filter-select {
    width: 100%;
    min-width: 100px;
}

.filter-input .MuiOutlinedInput-root {
    font-size: 12px;
    height: 32px;
}

.filter-input .MuiOutlinedInput-input {
    padding: 6px 8px;
}

.filter-select .MuiSelect-select {
    font-size: 12px;
    padding: 6px 8px;
    min-height: auto;
}

.filter-select .MuiOutlinedInput-root {
    height: 32px;
}

/* Responsive design */
@media (max-width: 768px) {
    .header-content {
        padding: 6px 8px;
        gap: 6px;
        min-width: 100px;
    }
    
    .header-label {
        font-size: 13px;
    }
    
    .header-icons {
        gap: 2px;
    }
    
    .sort-icons {
        gap: 0;
    }
    
    .filter-input,
    .filter-select {
        min-width: 80px;
    }
}

/* Table-specific styling */
table .filterable-header {
    border-bottom: 2px solid #e5e7eb;
    background-color: #f9fafb;
}

table .filterable-header:hover {
    background-color: #f3f4f6;
}

/* Active sort styling */
.filterable-header.active-sort {
    background-color: #eff6ff;
}

.filterable-header.active-sort .header-label {
    color: #1d4ed8;
}

.filterable-header.active-sort:hover {
    background-color: #dbeafe;
}

/* Filter active state */
.filterable-header.filter-active {
    background-color: #fef3c7;
}

.filterable-header.filter-active:hover {
    background-color: #fde68a;
}
