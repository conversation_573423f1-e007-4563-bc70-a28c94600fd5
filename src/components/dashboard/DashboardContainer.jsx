import React, { useState } from 'react'
import SideBar from './SideBar'
import DashboardHeader from './DashboardHeader'
import Notifications from '../../pages/dashboard/Notifications'

const DashboardContainer = ({ content, pageTitle }) => {
    const [mobileMenuOpen, setMobileMenuOpen] = useState(true);
   const [unreadCount, setUnreadCount] = useState(0);
    return (
        <div className='dashboard'>
            <SideBar mobileMenuOpen={mobileMenuOpen} setMobileMenuOpen={setMobileMenuOpen} />
            <div className="dashboard-content">
                <DashboardHeader
                 mobileMenuOpen={mobileMenuOpen}
                  setMobileMenuOpen={setMobileMenuOpen} 
                  pageTitle={pageTitle}
                  unreadCount={unreadCount}
                  setUnreadCount={setUnreadCount}
                   />
                <div className="content">
                    {content}
                </div>
            </div>
        </div>
    )
}

export default DashboardContainer;