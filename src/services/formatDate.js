import React from 'react';
import dayjs from 'dayjs';

/**
 * Formats a date consistently regardless of locale
 *
 * @param {Date|string|Object} dateInput - Date object, ISO string, or dayjs object
 * @param {string} format - Output format ('iso', 'display', or 'api')
 * @returns {string} Formatted date string
 */
export const formatDate = (dateInput, format = 'api') => {
    if (!dateInput) return "";

    try {
        // Handle dayjs object
        if (dateInput.$y && dateInput.$M !== undefined && dateInput.$D) {
            const year = dateInput.$y;
            const month = (dateInput.$M + 1).toString().padStart(2, '0'); // dayjs months are 0-indexed
            const day = dateInput.$D.toString().padStart(2, '0');

            console.log(`Formatting date from dayjs: ${year}-${month}-${day}`);

            if (format === 'display') {
                return `${month}-${day}-${year}`; // MM-DD-YYYY format as client requested
            } else if (format === 'iso') {
                return `${year}-${month}-${day}`;
            } else {
                return `${year}-${month}-${day}`; // API format
            }
        }

        // Handle Date object or string
        const date = new Date(dateInput);
        if (isNaN(date.getTime())) {
            console.error('Invalid date:', dateInput);
            return "";
        }

        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');

        console.log(`Formatting date from Date: ${year}-${month}-${day}`);

        if (format === 'display') {
            return `${month}-${day}-${year}`; // MM-DD-YYYY format as client requested
        } else if (format === 'iso') {
            return `${year}-${month}-${day}`;
        } else {
            return `${year}-${month}-${day}`; // API format
        }
    } catch (error) {
        console.error('Error formatting date:', error);
        return "";
    }
};

/**
 * Display a formatted date in a component
 */
const FormattedDate = ({ dateString, format = 'display' }) => {
    const formattedDate = formatDate(dateString, format);

    return (
        <div>
            <p>{formattedDate}</p>
        </div>
    );
};

export default FormattedDate;
