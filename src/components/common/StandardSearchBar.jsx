import React from 'react';
import { Search01Icon } from 'hugeicons-react';
import './StandardSearchBar.css';

const StandardSearchBar = ({ 
    placeholder = "Search...", 
    value, 
    onChange, 
    onSearch, 
    isLoading = false,
    disabled = false,
    className = ""
}) => {
    const handleInputChange = (e) => {
        if (onChange) {
            onChange(e.target.value);
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && onSearch) {
            onSearch();
        }
    };

    const handleSearchClick = () => {
        if (onSearch) {
            onSearch();
        }
    };

    return (
        <div className={`standard-search-bar ${className}`}>
            <div className="search-input-container">
                <input
                    type="text"
                    placeholder={placeholder}
                    value={value}
                    onChange={handleInputChange}
                    onKeyPress={handleKeyPress}
                    disabled={disabled}
                    className="search-input"
                />
            </div>
            <button
                type="button"
                onClick={handleSearchClick}
                disabled={disabled || isLoading}
                className="search-button"
            >
                <Search01Icon size={20} />
                <span>{isLoading ? 'Searching...' : 'Search'}</span>
            </button>
        </div>
    );
};

export default StandardSearchBar;
