.auth-pages {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
}
.auth-pages .page-content {
  width: 500px;
  display: grid;
  text-align: center;
  gap: 1rem;
}
.auth-pages .page-content .logo {
  max-width: 72px;
  margin: auto !important;
}
.auth-pages .page-content .documentation {
  background-color: #07AEEF;
  padding: 12px;
  border-radius: 10px;
  color: white;
  font-weight: bold;
  cursor: pointer;
}

.sidebar-doc {
  background-color: #07aeef;
  color: #fff;
  height: 100vh;
  min-width: 252px;
  position: sticky;
  width: 20%;
  overflow-y: auto;
}
.sidebar-doc .contents {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
}
.sidebar-doc .contents .dropdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.sidebar-doc .contents .dropdown a {
  cursor: pointer;
}
.sidebar-doc .contents .dropdown .login-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section {
  background-color: whitesmoke;
  border-radius: 12px;
  margin-right: 24px;
  padding: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 50px;
}
.section .text {
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 48px;
}
.section .text .listing {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.section .text .listing .number-text {
  display: flex;
  gap: 10px;
}
.section .text .listing .number-text .number {
  padding: 10px;
  background-color: #07AEEF;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}
.section .text .listing .number-text .email {
  color: #07AEEF;
  font-weight: bolder;
}
.section .image {
  width: 50%;
}
.section .image img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

@media screen and (max-width: 560px) {
  .auth-pages .page-content {
    width: 90vw;
  }
}/*# sourceMappingURL=authPages.css.map */