.user-import-container {
  position: relative;
   display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  
  .import-button {
    padding: 8px 16px;
    background-color: #07AEEF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;

    &:hover {
      background-color: #0698d1;
    }

    &:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
  }
}

.import-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.import-step {
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  position: relative;
   .title{
     display: flex;
 justify-content: space-between;
  padding: 24px;
   }
 
  
  .import-content {
    padding: 20px;
      display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  align-items: center;
    
    h3 {
      
      font-size: 16px;
      font-weight: 600;
    }
    
    p {
      color: #666;
      margin-top: 0;
      margin-bottom: 24px;
      font-size: 14px;
    }
  }
  
  .drop-area {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 40px 20px;
  
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    width: 100%;
    
    

    
    .browse-btn {
      background: none;
      border: none;
      color: #07AEEF;
      cursor: pointer;
      font-weight: 500;
      padding: 0;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .upload-btn, .import-btn, .list-btn {
    width: 100%;
    padding: 12px;
    background-color: #07AEEF;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    
    &:hover {
      background-color: #0698d1;
    }
    
    &:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
  }
  
  .mapping-container {
    margin-bottom: 24px;
    
    .mapping-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      font-weight: 500;
      font-size: 14px;
      color: #666;
    }
    
    .mapping-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .file-column, .property-column {
        flex: 1;
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 4px;
      }
      
      .arrow {
        margin: 0 12px;
        color: #666;
      }
    }
  }
  
  .preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0;
    
    .count-display {
      text-align: center;
      
      h1 {
        font-size: 48px;
        margin: 0;
        color: #333;
      }
      
      p {
        margin: 8px 0 0;
        color: #666;
      }
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 12px;
    
    .back-btn {
      flex: 1;
      padding: 12px;
      background-color: white;
      color: #333;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-weight: 500;
      cursor: pointer;
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
    
    .import-btn {
      flex: 1;
    }
  }
}

.step-4 {
  padding: 40px;
  text-align: center;
  
  .success-icon {
    color: #4CAF50;
    margin-bottom: 16px;
  }
  
  h2 {
    border: none;
    padding: 0;
    margin-bottom: 8px;
    font-size: 24px;
  }
  
  p {
    color: #666;
    margin-bottom: 24px;
  }
  
  .list-btn {
    background-color: #4CAF50;
    
    &:hover {
      background-color: #45a049;
    }
  }
}
