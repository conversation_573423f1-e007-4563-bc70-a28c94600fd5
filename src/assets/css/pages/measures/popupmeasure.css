.confirm-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.confirm-popup.show {
  opacity: 1;
  pointer-events: auto;
}

.popup-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
  min-width: 300px;
}

.popup-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}/*# sourceMappingURL=popupmeasure.css.map */