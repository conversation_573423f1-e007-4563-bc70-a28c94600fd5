.measure-tooltip-container {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.measure-name-with-tooltip {
    display: flex;
    align-items: center;
    gap: 4px;
}

.info-icon {
    color: #6b7280;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.measure-tooltip-container:hover .info-icon {
    opacity: 1;
    color: #3b82f6;
}

.measure-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    z-index: 1000;
    max-width: 300px;
    min-width: 200px;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
}

.tooltip-header {
    font-weight: 600;
    margin-bottom: 8px;
    color: #60a5fa;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tooltip-content {
    color: #e5e7eb;
}

.tooltip-arrow {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #1f2937;
}

.measure-tooltip.tooltip-top {
    top: 100%;
    bottom: auto;
    margin-top: 8px;
    margin-bottom: 0;
}

.measure-tooltip.tooltip-top .tooltip-arrow {
    top: -6px;
    border-top: none;
    border-bottom: 6px solid #1f2937;
}

@media (max-width: 768px) {
    .measure-tooltip {
        max-width: 250px;
        font-size: 13px;
        padding: 10px 12px;
    }
}

.table-cell-tooltip {
    width: 100%;
    text-align: left;
}

.table-cell-tooltip .measure-tooltip {
    left: 0;
    transform: none;
    white-space: normal;
}

.table-cell-tooltip .measure-tooltip .tooltip-arrow {
    left: 20px;
    transform: none;
}
