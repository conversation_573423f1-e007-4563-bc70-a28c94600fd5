import React, { useRef, useEffect } from 'react';
import { PlusSignIcon, Copy01Icon } from 'hugeicons-react';
import '../../../assets/css/components/forms/measure-action-dialog.css';
import { SecondaryButton } from '../buttons';

const MeasureActionDialog = ({ isOpen, onClose, onCreateNew, onCopyMeasures }) => {
    const dialogRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (dialogRef.current && !dialogRef.current.contains(e.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    return (
        <div className="measure-action-overlay">
            <div className="measure-action-dialog" ref={dialogRef}>
                <h2>Choose action</h2>
                <p>What would you like to do?</p>

                <div className="action-options">
                    <div className="action-option" onClick={onCreateNew}>
                        <div className="option-icon create-icon">
                            <PlusSignIcon size={20} />
                        </div>
                        <div className="option-content">
                            <h3>Create new measure</h3>
                            <p>New measure from scratch</p>
                        </div>
                    </div>

                    <div className="action-option" onClick={onCopyMeasures}>
                        <div className="option-icon copy-icon">
                            <Copy01Icon size={20} />
                        </div>
                        <div className="option-content">
                            <h3>Copy measure</h3>
                            <p>Existing measure to another year</p>
                        </div>
                    </div>
                </div>

                <div className="dialog-actions">
                    <SecondaryButton
                        buttonText="Cancel"
                        onClick={onClose}
                    />
                </div>
            </div>
        </div>
    );
};

export default MeasureActionDialog;
