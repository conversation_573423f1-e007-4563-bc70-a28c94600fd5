.error-pages {
  height: 100vh;
  width: 100vw;
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 100px;
}
.error-pages img {
  width: 100px;
  height: 80px;
  -o-object-fit: cover;
     object-fit: cover;
}
.error-pages .page-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  border: 4px solid #73c9ec;
  align-items: center;
  padding: 120px;
  width: 50%;
  border-radius: 10px;
}
.error-pages .page-content h1 {
  font-size: 24px !important;
  color: #07AEEF;
  text-align: center;
}/*# sourceMappingURL=error.css.map */