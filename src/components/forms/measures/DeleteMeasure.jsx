import React, { useState, useRef, useEffect } from 'react';
import { PrimaryButton, SecondaryButton } from '../buttons';
import api, { API_URL } from '../../../api';
import { Flag01Icon } from 'hugeicons-react';
import { toast } from 'react-toastify';

const DeleteMeasure = ({ measureId, synchronousId, isOpen, onClose, removeMeasure }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const popupRef = useRef(null);

  useEffect(() => {
      const handleClickOutside = (e) => {
          if (popupRef.current && !popupRef.current.contains(e.target)) {
              onClose(); 
          }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
          document.removeEventListener('mousedown', handleClickOutside);
      };
  }, [onClose]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      const response = await api.delete(`${API_URL}/measures/delete-measure/${measureId}/`);

      if (response.status === 204) {
        removeMeasure(measureId); 
        toast.success('Measure deleted successfully!');
      }
    } catch (error) {
      console.error('Error deleting measure:', error.response ? error.response.data : error.message);
      setError('Failed to delete measure. Please try again.');
    } finally {
      setIsSubmitting(false);
      onClose(); 
      window.location.reload(); 
    }
  };

  if (!isOpen) return null;

  return (
    <div className="overlay">
      <div className="popup-user" ref={popupRef}>
        <div className="popup-header">
          <Flag01Icon size={24} color="#FF3B30" />
          <h4 className="title">Delete</h4>
        </div>
        <p className="paragraph-red">Do you really want to delete this Measure? (With ID {synchronousId})</p>
        {error && <p className="error-message">{error}</p>}
        <div className="buttons">
          <SecondaryButton isLoading={false} onClick={onClose} buttonText={'Cancel'} />
          <PrimaryButton 
            isLoading={isSubmitting} 
            processingText={'Deleting'} 
            buttonText={'Delete'} 
            onClick={handleSubmit} 
          />
        </div>
      </div>
    </div>
  );
};


export default DeleteMeasure;

