.standard-filters {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 20px;
}

.filters-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    font-family: "Plus Jakarta Sans", sans-serif;
}

.filters-content {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: end;
}

.filters-actions {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
}

.filter-field {
    display: flex;
    flex-direction: column;
    gap: 6px;
    min-width: 150px;
    flex: 1;
}

.filter-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    font-family: "Plus Jakarta Sans", sans-serif;
}

.filter-select,
.filter-input {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    color: #374151;
    background-color: white;
    font-family: "Plus Jakarta Sans", sans-serif;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #07AEEF;
    box-shadow: 0 0 0 3px rgba(7, 174, 239, 0.1);
}

.filter-select:disabled,
.filter-input:disabled {
    background-color: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
}

.filter-select {
    cursor: pointer;
}

.filter-select:disabled {
    cursor: not-allowed;
}

/* Date range specific styles */
.date-range-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-input {
    flex: 1;
    min-width: 120px;
}

.date-separator {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
    white-space: nowrap;
}

/* Responsive design */
@media (max-width: 768px) {
    .filters-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-field {
        min-width: unset;
        width: 100%;
    }
    
    .date-range-inputs {
        flex-direction: column;
        gap: 8px;
    }
    
    .date-separator {
        display: none;
    }
    
    .standard-filters {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .standard-filters {
        padding: 12px;
    }
    
    .filters-title {
        font-size: 15px;
        margin-bottom: 12px;
    }
    
    .filter-select,
    .filter-input {
        padding: 8px 10px;
        font-size: 14px;
    }
}

/* Compact variant */
.standard-filters.compact {
    padding: 12px;
}

.standard-filters.compact .filters-title {
    font-size: 14px;
    margin-bottom: 12px;
}

.standard-filters.compact .filters-content {
    gap: 12px;
}

/* Inline variant */
.standard-filters.inline .filters-content {
    align-items: center;
}

.standard-filters.inline .filter-field {
    margin-bottom: 0;
}

/* No border variant */
.standard-filters.no-border {
    border: none;
    box-shadow: none;
    background-color: transparent;
    padding: 0;
}
