export const tableData = [
    {
      measureName: 'CAH 30 Day Re-admission Rate',
      bm: '< 2',
      status: '0.07',
      monthlyValues: ['0.00', '0.00', '0.05', '0.14', '0.00', '0.00', '0.14', '0.14', '0.14', '0.14', '0.14']
    },
    {
      measureName: 'ED 72 Hour Re-admission Rate',
      bm: '< 3',
      status: '0.15',
      monthlyValues: ['0.10', '0.12', '0.11', '0.14', '0.13', '0.15', '0.12', '0.10', '0.14', '0.16', '0.15']
    },
    {
      measureName: 'Left Without Being Seen',
      bm: '< 5',
      status: '0.22',
      monthlyValues: ['0.20', '0.21', '0.23', '0.22', '0.24', '0.25', '0.22', '0.21', '0.23', '0.22', '0.20']
    },
    {
      measureName: 'Left Against Medical Advice',
      bm: '< 4',
      status: '0.18',
      monthlyValues: ['0.15', '0.16', '0.17', '0.19', '0.20', '0.18', '0.19', '0.18', '0.17', '0.16', '0.18']
    },
    {
      measureName: 'ED Mortality Rate',
      bm: '< 6',
      status: '0.25',
      monthlyValues: ['0.22', '0.24', '0.23', '0.26', '0.25', '0.27', '0.26', '0.24', '0.23', '0.22', '0.24']
    },
    {
      measureName: 'Emergency Room Transfers',
      bm: '< 1',
      status: '0.10',
      monthlyValues: ['0.08', '0.09', '0.10', '0.11', '0.10', '0.09', '0.11', '0.10', '0.09', '0.08', '0.09']
    },
    {
      measureName: 'Acute/Swing Bed Transfers',
      bm: '< 7',
      status: '0.30',
      monthlyValues: ['0.29', '0.31', '0.32', '0.33', '0.30', '0.29', '0.30', '0.32', '0.33', '0.31', '0.30']
    },
    {
      measureName: 'Medication Errors',
      bm: '< 8',
      status: '0.20',
      monthlyValues: ['0.18', '0.19', '0.20', '0.22', '0.21', '0.20', '0.19', '0.21', '0.22', '0.23', '0.21']
    },
    {
      measureName: 'Inpatient Mortality Rate',
      bm: '< 9',
      status: '0.28',
      monthlyValues: ['0.27', '0.28', '0.29', '0.30', '0.29', '0.28', '0.30', '0.29', '0.28', '0.27', '0.30']
    },
    {
      measureName: 'Total Falls',
      bm: '< 4',
      status: '0.12',
      monthlyValues: ['0.10', '0.11', '0.12', '0.13', '0.11', '0.12', '0.13', '0.11', '0.10', '0.12', '0.13']
    },
    {
      measureName: 'Pressure Ulcer',
      bm: '< 5',
      status: '0.17',
      monthlyValues: ['0.16', '0.17', '0.18', '0.19', '0.18', '0.17', '0.16', '0.18', '0.19', '0.20', '0.18']
    }
  ];