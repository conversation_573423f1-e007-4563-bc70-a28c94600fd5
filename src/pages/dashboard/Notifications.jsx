import React, { useState, useEffect } from "react";

import toast from "react-hot-toast";
import api, { API_URL } from '../../api'
import DashboardContainer from "../../components/dashboard/DashboardContainer";
import "../../assets/css/notification/notification.css"
import { PrimaryButton, SecondaryButton } from "../../components/forms/buttons";
import Cookies from 'js-cookie';

function formatNotificationDate(inputDate) {
  const date = new Date(inputDate);
  const now = new Date();

  const differenceInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
  const options = { hour: 'numeric', minute: 'numeric', hour12: true };
  const time = date.toLocaleTimeString('en-US', options);

  if (differenceInDays === 0) {
    return `Today, ${time}`;
  } else if (differenceInDays === 1) {
    return `Yesterday, ${time}`;
  } else {
    const monthDay = date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
    return `${monthDay}, ${time}`;
  }
}

const NotificationsTable = ({
  Notifications = [],
  selectedNotifications,
  handleSelect,
  handleSelectAll,
  isAllSelected,
}) => {
  return (
    <div className="table-container">
      <table className="Notifications-table">
        <thead>
          <tr>
            <th className="input-check">
              <input
                type="checkbox"
                checked={isAllSelected}
                onChange={handleSelectAll}
              />
              Select All Notifications
            </th>
            <th>Category</th>
            <th>Status</th>
            <th>Summary</th>
            <th>Date/Time Created</th>
          </tr>
        </thead>
        <tbody>
          {Notifications.length > 0 ? (
            Notifications.map((Notification, index) => (
              <tr key={index}>
                <td>
                  <input
                    type="checkbox"
                    checked={isAllSelected || selectedNotifications.includes(index)}
                    onChange={() => handleSelect(index)}
                  />
                </td>
                <td>{Notification.category}</td>
                <td className={Notification.is_read ? "read" : "unread"}>
                  {Notification.is_read ? "Read" : "Unread"}
                </td>
                <td><b>{Notification.summary}</b></td>
                <td>{formatNotificationDate(Notification.date_time_created)}</td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="5" style={{ textAlign: "center" }}>
                No Notifications found
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

const NotificationPageContent = ({ updateUnreadCount }) => {
  const [notifications, setNotifications] = useState([]);
  const [selectedNotifications, setSelectedNotifications] = useState([]);
  const [nextPage, setNextPage] = useState(null);
  const [previousPage, setPreviousPage] = useState(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [allNotifications, setAllNotifications] = useState([]);
  const [isSelectingAll, setIsSelectingAll] = useState(false);

  const isAllSelected = isSelectingAll;

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const response = await api.get(`${API_URL}/notifications/`);
        setNotifications(response.data.results);
        setNextPage(response.data.next);
        setPreviousPage(response.data.previous);

        const fetchAllNotifications = async () => {
          try {
            const allResponse = await api.get(`${API_URL}/notifications/?page_size=1000`);
            setAllNotifications(allResponse.data.results);
          } catch (error) {
            console.error("Failed to load all notifications:", error);
          }
        };
        fetchAllNotifications();

        const unreadCount = response.data.results.filter(n => !n.is_read).length;
        setUnreadCount(unreadCount);
        updateUnreadCount(unreadCount);
      } catch (error) {
        console.error("Failed to load notifications:", error);
        toast.error("Failed to load notifications");
      }
    };

    fetchNotifications();
  }, []);

  const handleMarkAsRead = async () => {
    let ids;
    if (isSelectingAll) {
      ids = allNotifications.map(n => n.id);
    } else {
      ids = selectedNotifications.map(index => notifications[index]?.id).filter(Boolean);
    }
    
    if (ids.length === 0) {
      toast.error("No notifications selected for marking as read.");
      return;
    }
    
    try {
      await api.post(
        `${API_URL}/notifications/mark_as_read/`, {
        ids: ids,
        is_read: true
      });
      
      if (isSelectingAll) {
        setAllNotifications(prev => prev.map(n => ({ ...n, is_read: true })));
        setNotifications(prev => prev.map(n => ({ ...n, is_read: true })));
      } else {
        setNotifications(prev =>
          prev.map(n => ids.includes(n.id) ? { ...n, is_read: true } : n)
        );
      }
      
      setUnreadCount(0);
      updateUnreadCount(0);
      toast.success("Marked as read");
      window.location.reload();
    } catch (error) {
      console.error("Error marking notifications as read:", error);
      toast.error("Failed to update notifications");
    }
  };

  const handleDelete = async () => {
    let ids;
    if (isSelectingAll) {
      ids = allNotifications.map(n => n.id);
    } else {
      ids = selectedNotifications.map(index => notifications[index]?.id).filter(Boolean);
    }

    if (ids.length === 0) {
      toast.error("No notifications selected for deletion.");
      return;
    }

    try {
      await api.post(`${API_URL}/notifications/delete/`, { ids });

      if (isSelectingAll) {
        setAllNotifications([]);
        setNotifications([]);
        setUnreadCount(0);
        updateUnreadCount(0);
      } else {
        const updatedNotifications = notifications.filter(n => !ids.includes(n.id));
        setNotifications(updatedNotifications);
        const newUnreadCount = updatedNotifications.filter(n => !n.is_read).length;
        setUnreadCount(newUnreadCount);
        updateUnreadCount(newUnreadCount);
      }

      toast.success("Notifications deleted successfully");
      window.location.reload();
    } catch (error) {
      console.error("Error deleting notifications:", error);
      toast.error("Failed to delete notifications");
    }
  };

  const handleSelectAll = () => {
    setIsSelectingAll(!isSelectingAll);
    if (!isSelectingAll) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications([]);
    }
  };

  const handleSelect = (index) => {
    if (isSelectingAll) {
      setIsSelectingAll(false);
    }
    
    setSelectedNotifications(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const changePage = async (url) => {
    if (!url) return;

    try {
      const response = await api.get(url);
      setNotifications(response.data["results"]);
      setNextPage(response.data["next"]);
      setPreviousPage(response.data["previous"]);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load notifications.");
    }
  };

  return (
    <div className="Notifications-page">
      <div className="Notifications">
        <h1>Notifications</h1>
        <div className="buttons">
          <SecondaryButton
            onClick={handleMarkAsRead}
            buttonText='Mark as Read'
          />
          <PrimaryButton onClick={handleDelete}
            buttonText='Delete Notification'
          />
        </div>
      </div>

      <NotificationsTable
        Notifications={notifications}
        selectedNotifications={selectedNotifications}
        handleSelect={handleSelect}
        handleSelectAll={handleSelectAll}
        isAllSelected={isAllSelected}
      />

      <div className="pagination">
        <button
          className="buttons prev-btn"
          onClick={() => changePage(previousPage)}
          disabled={!previousPage}
        >
          Previous
        </button>
        <button
          className="buttons next-btn"
          onClick={() => changePage(nextPage)}
          disabled={!nextPage}
        >
          Next
        </button>
      </div>
    </div>
  );
};

const Notifications = () => {
  const [unReadCount, setUnreadCount] = useState(0);
  const handleUnreadCountUpdate = (newCount) => {
    setUnreadCount(newCount);
  };

  return (
    <DashboardContainer content={<NotificationPageContent updateUnreadCount={handleUnreadCountUpdate} />} pageTitle={'Notifications'} />
  );
};

export default Notifications;
