import React, { useState } from 'react'
import Cookies from 'js-cookie'
import { Helm<PERSON> } from 'react-helmet'
import { Link } from 'react-router-dom'
import { siteTitle } from '../../api'
import { TextInput } from '../../components/forms/Input'
import { IdVerifiedIcon, LockPasswordIcon, Mail01Icon } from 'hugeicons-react'
import { PrimaryButton } from '../../components/forms/buttons'
import toast from 'react-hot-toast'
import { developers } from '../../data/developers'

const UnderMaintenance = () => {
    const [isLoading, setIsLoading] = useState(false)
    const [showVerificationCode, setShowVerificationCode] = useState(false)
    const [email, setEmail] = useState("")
    const handleVerify = async (e) => {
        if (!email) {
            toast.error('Email is required')
            return
        }
        if (!developers.includes(email)) {
            toast.error('We cannot verify your email address')
            return
        }
        setIsLoading(true)
        setTimeout(() => {
            Cookies.set('canAccessSystemUnderMaintenance', true)
            setIsLoading(false)
            window.location.reload()
            // call api to verify that the user is allowed to access the system while under maintenance and save it in the cookies.
            // Then refresh the page
        }, 2000);
    }
    return (
        <>
            <Helmet>
                <title>{siteTitle} | Under maintenance</title>
            </Helmet>

            <div className="maintenance-page">
                <div className="container">
                    {
                        showVerificationCode
                            ? <>
                                <div className="form">
                                    <h1>Enter your email for verification</h1>
                                    <TextInput iconClass={<Mail01Icon />} type={'email'} placeholder="Enter your email here" value={email} setValue={setEmail} />
                                    <PrimaryButton iconClass={<IdVerifiedIcon />} buttonText={'Verify'} isLoading={isLoading} processingText={'Verifying'} onClick={handleVerify} />
                                </div>
                            </>
                            : <>
                                <img src="/logo192.png" alt="logo" className="logo" />
                                <h1>Under maintenance</h1>
                                <p>
                                    Our team is currently working on updates to the system to improve performance, experience and features
                                </p>

                                <img src="/images/shape.svg" alt="shape" className="shape" />
                                <PrimaryButton onClick={() => setShowVerificationCode(true)} buttonText={'Verify access'} />
                            </>
                    }
                </div>
            </div>
        </>
    )
}

export default UnderMaintenance
