@import '../variables';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}


.date-range-display {
  margin-top: 10px;
  font-weight: bold;
  color: #333;
}

.text-area {
  border: 1px solid blue;
}

.date-range {
  display: flex;
  gap: 10px;

  .date {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}
.values{
  display: flex;
  flex-direction: column;
  gap: 4px;
  label{
    color: gray;
  }
  .label-text{
    display: flex;
    gap: 10px;
    align-items: center;

  }
  .symbols{
    border: 1px solid #e8e7e7;
    padding: 10px;
    color: gray;
    border-radius: 0.4rem;
  }
}

a {
  text-decoration: none;
  color: inherit;
}

.table-container {
  overflow-x: auto;
}

table {
  border-collapse: collapse;
  width: 100%;


  tr {


    td,
    th {
      border: 1px solid #ccc;
      padding: 10px 20px;
      text-align: start;
    }

    .td-img {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 18px;
      border: 0.5px solid #ccc;

      img {
        border: 1px solid #ccc;
      }
    }

    .bm {
      background-color: #44536a;
      color: #ffffff;
    }
  }

  .draggable {
    background: #571616;
    border: 1px solid #7f5151;
    cursor: grab;
  }

  .draggable:hover {
    background: #df5656;
  }

  .droppable {
    // min-height: 50px;
    background: #612626;
  }
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;

  .popup-content {
    background-color: white;
    // width: 528px;
    padding: 2rem;
    border-radius: 1rem;
  }
}


.maintenance-page {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  .container {
    background-color: #FBFEFF;
    border: 6px solid #F6FCFF;
    padding: 5rem 9rem;
    display: grid;
    align-content: center;
    justify-items: center;
    gap: 2rem;
    width: 60vw;
    border-radius: 40px;

    .logo {
      max-width: 100px;
    }

    .shape {
      width: 100%;
    }

    h1 {
      color: $primary-color;
    }
  }
}

.popup-buttons {
  display: flex;
  gap: 24px;
}

@media screen and (max-width: 840px) {
  .maintenance-page {
    padding: 2rem;

    .container {
      width: unset;
      max-width: 600px;
      padding: 2rem;
      border-radius: 1rem;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}