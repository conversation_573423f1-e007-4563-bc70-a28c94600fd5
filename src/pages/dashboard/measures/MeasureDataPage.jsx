import React, { useState, useEffect, useRef } from 'react';
import { PrimaryButton, SecondaryButton } from '../../../components/forms/buttons';
import { CleanIcon, Edit02Icon, Delete01Icon, PlusSignIcon, Search01Icon, TableIcon } from 'hugeicons-react';
import { DataListInput, TextInput } from '../../../components/forms/Input';
import api, { API_URL } from '../../../api';
import toast from 'react-hot-toast';
import FormattedDate from '../../../services/formatDate';
import AddNewMeasureData from '../../../components/forms/measures/MeasureData/AddNewMeasureData';
import BulkAddMeasureData from '../../../components/forms/measures/MeasureData/BulkAddMeasureData';
import EditMeasureData from '../../../components/forms/measures/MeasureData/EditMeasureData';
import MeasureTooltip from '../../../components/common/MeasureTooltip';
import SortableTableHeader from '../../../components/common/SortableTableHeader';
import FilterableTableHeader from '../../../components/common/FilterableTableHeader';
import DeleteMeasureData from '../../../components/forms/measures/MeasureData/DeleteMeasureData';
import '../../../assets/css/pages/measures/measures.css';
import '../../../assets/css/components/forms/bulk-measure-data.css';
import TableLoading from '../../../components/loading/TableLoading';
import DashboardContainer from '../../../components/dashboard/DashboardContainer';
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { hasRole } from '../../../services/userPosition';


const MeasuresTable = ({ measures = [], onEditClick, onDeleteClick, onReorder, onSort, currentSort, onFilter, filters }) => {
    const handleDragEnd = (result) => {
        const { source, destination } = result;
        if (!destination) return;

        if (source.index === destination.index) return;

        const updatedMeasures = Array.from(measures);
        const [removed] = updatedMeasures.splice(source.index, 1);
        updatedMeasures.splice(destination.index, 0, removed);

        onReorder(updatedMeasures);
    };

    const [selectedMeasures, setSelectedMeasures] = useState([]);

    const handleSelectMeasure = (measureId) => {
        const index = selectedMeasures.indexOf(measureId);
        if (index === -1) {
            setSelectedMeasures([...selectedMeasures, measureId]);
        } else {
            setSelectedMeasures(selectedMeasures.filter((id) => id !== measureId));
        }
    };


    return !measures || !Array.isArray(measures) || measures.length === 0 ? (
        "No Data Found..."
    ) : (

        <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="measuresTable" className="table-container">
                {(provided) => (
                    <div
                        className="table-container"
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                    >
                        <table className='draggable'>
                            <thead>
                                <tr>
                                    <FilterableTableHeader
                                        label="Measure ID"
                                        sortKey="synchronous_id"
                                        currentSort={currentSort || { key: '', direction: 'asc' }}
                                        onSort={onSort || (() => {})}
                                        onFilter={onFilter || (() => {})}
                                        filterValue={filters?.synchronous_id || ''}
                                        filterType="text"
                                    />
                                    <FilterableTableHeader
                                        label="Measure Name"
                                        sortKey="name"
                                        currentSort={currentSort || { key: '', direction: 'asc' }}
                                        onSort={onSort || (() => {})}
                                        onFilter={onFilter || (() => {})}
                                        filterValue={filters?.name || ''}
                                        filterType="text"
                                    />
                                    <FilterableTableHeader
                                        label="Value"
                                        sortKey="value"
                                        currentSort={currentSort || { key: '', direction: 'asc' }}
                                        onSort={onSort || (() => {})}
                                        onFilter={onFilter || (() => {})}
                                        filterValue={filters?.value || ''}
                                        filterType="text"
                                    />
                                    <FilterableTableHeader
                                        label="Reporting Period Range"
                                        sortKey="starting_date"
                                        currentSort={currentSort || { key: '', direction: 'asc' }}
                                        onSort={onSort || (() => {})}
                                        onFilter={onFilter || (() => {})}
                                        filterValue={filters?.starting_date || ''}
                                        filterType="text"
                                    />
                                    <FilterableTableHeader
                                        label="Hospital"
                                        sortKey="hospital"
                                        currentSort={currentSort || { key: '', direction: 'asc' }}
                                        onSort={onSort || (() => {})}
                                        onFilter={onFilter || (() => {})}
                                        filterValue={filters?.hospital || ''}
                                        filterType="text"
                                    />
                                    {
                                        !hasRole(["Admin", "Super User"]) ?
                                            <></>
                                            :
                                            <th>Actions</th>
                                    }

                                </tr>
                            </thead>
                            <tbody>
                                {measures.map((measure, index) => (
                                    <Draggable
                                        key={measure.measure_data_id}
                                        draggableId={measure.measure_data_id.toString()}
                                        index={index}
                                    >
                                        {(provided) => (
                                            <tr
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                            >
                                                <td>{measure.synchronous_id}</td>
                                                <td>
                                                    <MeasureTooltip
                                                        measureName={measure.name}
                                                        description={measure.description || measure.measure_description}
                                                    />
                                                </td>
                                                <td>{measure.value}</td>
                                                <td style={{ whiteSpace: "nowrap", display: "flex", gap: 8, padding: 24 }}>
                                                    {/* <FormattedDate dateString={measure.starting_date} format="display" /> -
                                                    <FormattedDate dateString={measure.end_date} format="display" /> */}
                                                    {measure.starting_date} - {measure.end_date}
                                                </td>
                                                <td>{measure.hospital || "N/A"}</td>

                                                {!hasRole(["Admin", "Super User"]) ?
                                                    <></>

                                                    :

                                                    <td>
                                                        <div className="action-buttons">
                                                            <Edit02Icon
                                                            size={32}
                                                                className="edit-icon"
                                                                onClick={() => onEditClick(measure.measure_data_id)}
                                                            />
                                                            {hasRole(["Admin", "Super User"]) && (
                                                                <Delete01Icon
                                                                size={32}
                                                                    className="delete-icon"
                                                                    onClick={() => onDeleteClick(measure.measure_data_id)}
                                                                />
                                                            )}
                                                        </div>
                                                    </td>

                                                }

                                            </tr>
                                        )}
                                    </Draggable>
                                ))}
                                {provided.placeholder}
                            </tbody>
                        </table>
                    </div>
                )}
            </Droppable>
        </DragDropContext>
    );
};

function isValidSearch(query) {
    const trimmedQuery = query.trim();

    if (trimmedQuery.length > 2) {
        return true;
    }
    return false;
}
const MeasureDataContent = () => {
    const [measuresData, setMeasuresData] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [sortedData, setSortedData] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [showNewMeasureForm, setShowNewMeasureForm] = useState(false);
    const [showBulkMeasureForm, setShowBulkMeasureForm] = useState(false);
    const [constMeasureData, setConstMeasureData] = useState([]);
    const debounceTimeout = useRef(null);
    const [hasSearched, setHasSearched] = useState(false);
    const [editMeasureData, setEditMeasureData] = useState(null);
    const [deleteMeasureData, setDeleteMeasureData] = useState(null);
    const [totalItems, setTotalItems] = useState(0);
    const [nextPage, setNextPage] = useState(null)
    const [previousPage, setPreviousPage] = useState(null);
    const [currentDateYear, setCurrentDateYear] = useState(new Date().getFullYear());
    const [prevYear, setPrevYear] = useState(new Date().getFullYear() - 1);
    const [prevYearMinusTwo, setPrevYearMinusTwo] = useState(new Date().getFullYear() - 2);
    // const [prevYearMinusThree, setprevYearMinusThree] = useState(new Date().getFullYear() - 3);
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    // const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
    const [isOverlayVisible, setIsOverlayVisible] = useState(false);
    const [selectedHospital, setSelectedHospital] = useState('');
    const [measureName, setMeasureName] = useState('');
    const [selectedMonth, setSelectedMonth] = useState('');
    const [hospitals, setHospitals] = useState([]);
    const [currentSort, setCurrentSort] = useState({ key: '', direction: 'asc' });
    const [filters, setFilters] = useState({});



    useEffect(() => {
        const fetchHospitals = async () => {
            try {
                const response = await api.get(`${API_URL}/hospitals/`);

                setHospitals(response.data);

                if (response.data.length ===1) {
                    setSelectedHospital(response.data[0].name);
                }


            } catch (error) {

                toast.error('Failed to load hospitals');
            }
        };
        fetchHospitals();
    }, []);



    const handleKeyDown = (event) => {
        if (event.key === 'Enter') {
            handleSearch(searchQuery);
        }
    };
    const handleEditClick = (measure_data_id) => {
        const selectedMeasure = measuresData.find(measure => measure.measure_data_id === measure_data_id);
        setEditMeasureData(selectedMeasure);
    };
    const handleDeleteClick = (measure_data_id) => {
        setDeleteMeasureData(measure_data_id);
    };

    const handleDurationYearChange = (value) => {
        setSelectedYear(value)
    };
    const handleSearch = async () => {
        setIsSearching(true);

        try {
            const response = await api.get(`${API_URL}/measures/measures_hospital_list/`, {
                params: {
                    search: searchQuery || undefined,
                    measure_year: selectedYear || undefined,
                    hospital_name: selectedHospital || undefined,
                    month: selectedMonth || undefined
                }
            });

            console.log("Fetched Data:", response.data.results);

            const sortedMeasures = response.data.results.sort(
                (a, b) => new Date(a.date_created) - new Date(b.date_created)
            );

            setMeasuresData(sortedMeasures);
            setSortedData(sortedMeasures);
            setHasSearched(true);
            setNextPage(response.data.next);
            setPreviousPage(response.data.previous);

        } catch (err) {
            console.error('Error fetching data:', err);
        }

        setIsSearching(false);
    };


    const handleInputChange = (value) => {
        setSearchQuery(value);
        clearTimeout(debounceTimeout.current);
        debounceTimeout.current = setTimeout(() => {
            handleSearch(value);
        }, 300);
    };


    const handleReorder = (updatedMeasures) => {
        setMeasuresData(updatedMeasures);
    };

    const fetchMeasures = async () => {
        setIsOverlayVisible(true);
        try {
            const response = await api.get(`${API_URL}/measures/measures_hospital_list/`,
                {
                    params: {
                        search: searchQuery,
                        measure_year: selectedYear,
                        hospital_name: selectedHospital || undefined,
                        month: selectedMonth || undefined

                    }


                }
            );

            const sortedMeasures = response.data["results"].sort((a, b) => new Date(a.date_created) - new Date(b.date_created));
            const next = response.data["next"]
            const previous = response.data["previous"]

            setMeasuresData(sortedMeasures);
            setSortedData(sortedMeasures);
            setConstMeasureData(sortedMeasures);
            setMeasuresData(response.data["results"]);
            setNextPage(next)
            setPreviousPage(previous)
            setTotalItems(response.data.count);
        } catch (error) {
            console.error('Error fetching measures:', error);
        }
        finally {
            setIsOverlayVisible(false);
        }
    };


    const changePage = async (url) => {
        try {
            const response = await api.get(`${url}`);

            const sortedMeasures = response.data["results"].sort((a, b) => new Date(a.date_created) - new Date(b.date_created));
            setMeasuresData(sortedMeasures);
            setSortedData(sortedMeasures);
            setHasSearched(true);
            setNextPage(response.data["next"])
            setPreviousPage(response.data["previous"])
        } catch (err) {
            console.error('Error fetching data:', err);
        }
        setIsSearching(false);
    }



    useEffect(() => {
        fetchMeasures();
        return () => {
            clearTimeout(debounceTimeout.current);
        };
    }, [selectedYear, selectedHospital, selectedMonth]);

    const handleClearSearch = () => {
        setSearchQuery('');
        setMeasuresData(constMeasureData);
        setHasSearched(false);

    };

    const handleSort = (sortConfig) => {
        setCurrentSort(sortConfig);

        const sortedData = [...measuresData].sort((a, b) => {
            const aValue = a[sortConfig.key] || '';
            const bValue = b[sortConfig.key] || '';

            // Handle different data types
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
                return sortConfig.direction === 'asc' ? comparison : -comparison;
            }

            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
            }

            // Default string comparison
            const comparison = String(aValue).localeCompare(String(bValue));
            return sortConfig.direction === 'asc' ? comparison : -comparison;
        });

        setMeasuresData(sortedData);
    };

    const handleFilter = (column, value) => {
        const newFilters = { ...filters, [column]: value };
        setFilters(newFilters);

        // Apply filters to the current data
        let filteredData = [...measuresData];

        Object.keys(newFilters).forEach(key => {
            const filterValue = newFilters[key];
            if (filterValue) {
                filteredData = filteredData.filter(measure => {
                    const measureValue = measure[key] || '';
                    return measureValue.toString().toLowerCase().includes(filterValue.toLowerCase());
                });
            }
        });

        setMeasuresData(filteredData);
    };

    return (
        <div className='measures-page'>
            {showNewMeasureForm && (
                <div className="popup new-measure-form-popup">
                    <div className="popup-content">
                        <AddNewMeasureData
                            setShowNewMeasureFrom={setShowNewMeasureForm}
                            onClose={() => setShowNewMeasureForm(false)}
                            onAddSuccess={fetchMeasures}
                            year={selectedYear}
                        />
                    </div>
                </div>
            )}

            {showBulkMeasureForm && (
                <BulkAddMeasureData
                    onClose={() => setShowBulkMeasureForm(false)}
                    onAddSuccess={fetchMeasures}
                />
            )}
            {editMeasureData && (
                <div className="popup edit-measure-data-popup">
                    <div className="popup-content">
                        <EditMeasureData
                            measure_data_id={editMeasureData.measure_data_id}
                            measureData={editMeasureData}
                            onClose={() => setEditMeasureData(null)}
                            onSubmit={fetchMeasures}
                            selectedYear={selectedYear}
                        />
                    </div>
                </div>
            )}

            {
                deleteMeasureData && (
                    <div className="popup delete-measure-data-popup">
                        <div className="popup-content">
                            <DeleteMeasureData
                                measureId={deleteMeasureData}
                                isOpen={!!deleteMeasureData}
                                onClose={() => setDeleteMeasureData(null)}
                                removeMeasure={fetchMeasures}
                            />
                        </div>
                    </div>
                )
            }

            <div className="search-filter measure-data-filters">
                <div className="filters ">
                    <TextInput
                        iconClass={<Search01Icon />}
                        type='search'
                        placeholder='Search by measure name'
                        value={searchQuery}
                        setValue={handleInputChange}
                        onKeyDown={handleKeyDown}
                    />
                    <PrimaryButton
                        isLoading={isSearching}
                        onClick={() => handleSearch(searchQuery)}
                        buttonText={isSearching ? 'Searching...' : 'Search'}
                    />
                    {hasSearched && (
                        <SecondaryButton
                            iconClass={<CleanIcon />}
                            buttonText='Reset'
                            onClick={handleClearSearch}
                        />
                    )}

                    {hospitals && hospitals.length > 0 && (

                        <Select className='search-menu'
                            iconClass={<Search01Icon  />}
                            value={selectedHospital}
                            onChange={(e) => setSelectedHospital(e.target.value)}
                            displayEmpty
                            placeholder="Select Hospital"


                        >


                            {
                                hospitals.length > 1?
                                <MenuItem value=''>All Hospitals</MenuItem>:
                                <MenuItem key={hospitals[0].id} value={hospitals[0].name} >{hospitals[0].name}</MenuItem>
                            }

                                {hospitals?.map((hospital) => (
                                    hospitals.length >1  &&
                                <MenuItem key={hospital.id} value={hospital.name}>
                                    {hospital.name}
                                </MenuItem>

                            ))}








                        </Select>
                    )}

                    <Select
                        onChange={(e) => {
                            handleDurationYearChange(e.target.value);
                        }}
                        name="duration"
                        id="duration"
                        value={selectedYear}
                    >
                        <MenuItem value={currentDateYear}>{currentDateYear}</MenuItem>
                        <MenuItem value={prevYear}>{prevYear}</MenuItem>
                        <MenuItem value={prevYearMinusTwo}>{prevYearMinusTwo}</MenuItem>
                        {/* <MenuItem value={prevYearMinusThree}>{prevYearMinusThree}</MenuItem> */}
                    </Select>

                    <Select
                        value={selectedMonth}
                        onChange={(e) => setSelectedMonth(e.target.value)}
                        displayEmpty
                    >
                        <MenuItem value="">Filter by Month</MenuItem>
                        {Array.from({ length: 12 }, (_, i) => (
                            <MenuItem key={i + 1} value={i + 1}>
                                {new Date(0, i).toLocaleString('default', { month: 'long' })}
                            </MenuItem>
                        ))}
                    </Select>
                </div>
                <div className="action">
                    {!hasRole(["Admin", "Super User", "Manager"]) ?
                        <></>
                        :
                        <div className="action-buttons">
                            <Select
                                value=""
                                onChange={(e) => {
                                    if (e.target.value === 'single') {
                                        setShowNewMeasureForm(true);
                                    } else if (e.target.value === 'multiple') {
                                        setShowBulkMeasureForm(true);
                                    }
                                }}
                                displayEmpty
                                className="add-measure-select"
                            >
                                <MenuItem value="" disabled>
                                    <PlusSignIcon size={16} style={{ marginRight: '8px' }} />
                                    Add New Measure Data
                                </MenuItem>
                                <MenuItem value="single">Single</MenuItem>
                                <MenuItem value="multiple">Multiple</MenuItem>
                            </Select>
                        </div>
                    }
                </div>
            </div>

            <div className="measures table-container">
                {isOverlayVisible && <TableLoading />}
                <MeasuresTable
                    measures={measuresData}
                    onEditClick={handleEditClick}
                    onDeleteClick={handleDeleteClick}
                    onReorder={handleReorder}
                    onSort={handleSort}
                    currentSort={currentSort}
                    onFilter={handleFilter}
                    filters={filters}
                />
            </div>

            <div className="pagination">
                <button
                    className='buttons prev-btn'
                    onClick={() => changePage(previousPage)}
                    disabled={previousPage === null}
                >
                    Previous
                </button>

                {/* <span>Page {currentPage} of {totalPages}</span>  */}

                <button
                    className='buttons next-btn'
                    onClick={() => changePage(nextPage)}
                    disabled={nextPage === null}
                >
                    Next
                </button>

            </div>


        </div>
    );
};

const MeasureDataPage = () => {
    return (
        <DashboardContainer content={<MeasureDataContent />} pageTitle={'Measure Data'} />
    );
};

export default MeasureDataPage;
