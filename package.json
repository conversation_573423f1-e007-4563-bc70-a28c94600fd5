{"name": "new_kpi_front_end", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^3.27.0", "@azure/msal-react": "^2.2.0", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@mui/lab": "^5.0.0-alpha.173", "@mui/x-charts": "^7.12.1", "@mui/x-data-grid": "^7.12.1", "@mui/x-date-pickers": "^7.12.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "hugeicons-react": "^0.3.0", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "new_kpi_front_end": "file:", "papaparse": "^5.4.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-csv": "^2.2.2", "react-datepicker": "^7.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.26.0", "react-scripts": "5.0.1", "react-toastify": "^10.0.5", "recharts": "^2.12.7", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}