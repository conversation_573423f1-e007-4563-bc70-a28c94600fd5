.popup-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 500px;
  width: 100%;
}
.popup-content h2 {
  color: #333;
}

.copy-measures-modal {
  background-color: #fff;
}
.copy-measures-modal .modal-content {
  background-color: #fff;
}
.copy-measures-modal .form-control {
  margin-bottom: 20px;
  background-color: #fff;
}
.copy-measures-modal .form-control label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}
.copy-measures-modal .form-control select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
}
.copy-measures-modal .measures-list {
  border: 1px solid #ccc;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background-color: #fff;
}
.copy-measures-modal .measures-list .select-all {
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fff;
}
.copy-measures-modal .measures-list .measure-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  gap: 8px;
  background-color: #fff;
}
.copy-measures-modal .action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #fff;
}
.copy-measures-modal .action-buttons button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}
.copy-measures-modal .action-buttons button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.copy-measures-modal .action-buttons .cancel-button {
  background-color: transparent;
  border: 1px solid #ccc;
  color: #333;
}
.copy-measures-modal .action-buttons .copy-button {
  background-color: #07AEEF;
  border: none;
  color: white;
}/*# sourceMappingURL=copyMeasuresModal.css.map */