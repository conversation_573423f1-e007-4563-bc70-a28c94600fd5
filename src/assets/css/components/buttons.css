button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  border-color: transparent;
  font-size: inherit;
  border-radius: 0.4rem;
  text-wrap: nowrap;
}

.primary-button {
  background-color: #07AEEF;
  border: 1px solid #07AEEF;
  color: white;
  transition: all linear 0.2s;
}
.primary-button:hover {
  background-color: white;
  border: 1px solid #07AEEF;
  color: #07AEEF;
}

.secondary-button {
  background-color: transparent;
  border: 1px solid #0E76BC;
  color: #0E76BC;
}
.secondary-button:hover {
  background-color: #0E76BC;
  border: 1px solid white;
  color: white;
}

@media screen and (max-width: 540px) {
  .primary-button {
    padding: 0.6rem 0.2rem;
    width: 100%;
  }
}/*# sourceMappingURL=buttons.css.map */