@import '../../variables';
$sidebar-width: 252px;

.dashboard {
    display: flex;
    align-items: start;

    .sidebar-logout {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        background-color: $primary-color;
        height: 100vh;
        color: white;

        .side-bar {
            min-width: $sidebar-width;
            position: sticky;

            .close-menu {
                display: none;
                position: absolute;
                top: 1rem;
                right: 1rem;
                cursor: pointer;
                z-index: 999;
                color: white;

            }

            .branding {
                display: flex;
                align-items: center;
                font-size: 1rem;
                height: 4rem;
                padding: 1rem;
                gap: 0.5rem;
            }

            .menu-items {
                display: grid;
                height: fit-content;

                .menu-item {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    padding: 0.8rem;

                    &:hover {
                        background-color: white;
                        color: $primary-color;

                        .initials {
                            border-color: $primary-color;
                        }
                    }
                }

            }

            .active {
                background-color: white;
                color: $primary-color;
            }
        }

        .profile-item {
            display: flex;
            gap: 12px;
            padding: 24px;
            cursor: pointer;
        }

    }


    .dashboard-content {
        flex: 1;
        width: calc(100vw - sidebar-width);
        background-color: whitesmoke;
        overflow: auto;

        .dashboard-header {
            height: 6rem;
            width: 100%;
            background-color: white;
            padding: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: sticky;
            top: 0;
            z-index: 2;

            .openMenu {
                display: none;
            }

            .breadcrumbs {
                display: grid;
                height: fit-content;
                gap: 0.5rem;

                .menu-title {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                }

                .breadcrumb-list {
                    display: flex;
                    align-items: center;
                    gap: 0.4rem;

                    .breadcrumb {
                        display: flex;
                        text-transform: capitalize;
                        align-items: center;
                        gap: 0.4rem;
                        color: gray;

                        &:last-child {
                            color: $primary-color;
                        }
                    }
                }
            }

            .header-content {
                flex: 1;
                padding: 0.6rem 1rem;
                border-radius: 0.2rem;
                display: flex;
                align-items: center;
                justify-content: space-between;


                .profile-and-links {
                    display: flex;
                    align-items: center;
                    gap: 1rem;

                    .profile {
                        img {
                            height: 2.8rem;
                            width: 2.8rem;
                            padding: 0.2rem;
                            border-radius: 50%;
                            border: 1px solid #E5E5E5;
                            background-color: white;
                            object-fit: contain;
                        }
                    }

                    .profile-overlay {
                        background-color: rgba(0, 0, 0, 0.5);
                        width: 100%;
                        height: 100vh;
                        position: fixed;
                        top: 0;
                        left: 0;
                        z-index: 1;
                    }

                    .profile-drop-down {
                        display: flex;
                        flex-direction: column;
                        gap: 24px;
                        background-color: white;
                        position: absolute;
                        top: 80px;
                        right: 10px;
                        z-index: 2;
                        padding: 40px;
                        border-radius: 5px;

                        .profile-name {
                            display: flex;
                            gap: 12px;
                            align-items: center;
                            justify-content: center;

                            .name-position {
                                display: flex;
                                flex-direction: column;
                                gap: 10px;
                            }

                            img {
                                height: 2.8rem;
                                width: 2.8rem;
                                border-radius: 50%;
                                border: 1px solid #E5E5E5;
                                background-color: #E5E5E5;
                                object-fit: cover;
                            }
                        }

                        .profile-menu {
                            display: flex;
                            flex-direction: column;
                            gap: 10px;

                            .profile-item {
                                cursor: pointer;
                                display: flex;
                                gap: 10px;
                                align-items: center;
                            }
                        }
                    }

                    .notification {
                        position: relative;
                        display: inline-block;

                        .dot {
                            position: absolute;
                            top: -5px;
                            right: -5px;
                            height: 10px;
                            width: 10px;
                            background-color: red;
                            border-radius: 50%;
                        }

                        .unread-count {
                            position: absolute;
                            top: -10px;
                            right: -15px;
                            background-color: red;
                            color: white;
                            font-size: 12px;
                            padding: 2px 6px;
                            border-radius: 12px;
                        }
                    }






                }
            }
        }

        .content {
            padding: 1rem;
            height: calc(100vh - 7rem);
        }
    }
}

.drop-down {
    max-height: 3.5rem;
    overflow: hidden;
    transition: all 0.5s linear;

    .drop-down-icon {
        transform: rotate(0);
        transition: all 0.5s linear;
    }

    max-height: 100vh;



    .drop-down-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 1rem;

        .title {
            display: flex;
            align-items: center;

            justify-content: space-between;
            padding: 1rem;
            gap: 1rem;
            cursor: pointer;
        }
    }

    .drop-down-content {
        background-color: rgba(255, 255, 255, 0.138);
        margin: 1rem;
        border-radius: 8px;

        .menu-item {
            display: none;
        }
    }
}

.initials {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.5rem;
    width: 1.5rem;
    border: 1px solid white;
    border-radius: 0.2rem;
}

.sidebar-logout.hidden {
    display: none;
}


@media screen and (max-width: 1080px) {
    .dashboard {
        .sidebar-logout {

            .side-bar {
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
                z-index: 2000;
                width: 30%;
                height: 100vh;
                position: absolute;
                background-color: #07AEEF;

                .close-menu {
                    display: block;
                }
            }

            &.show {
                .side-bar {
                    transform: translateX(0);
                }
            }

            &.hide {
                .side-bar {
                    transform: translateX(-100%);
                }
            }
        }

        .dashboard-content {
            width: 100%;

            .dashboard-header {
                .openMenu {
                    display: block;
                }
            }
        }
    }
}





@media screen and (max-width: 560px) {
    .dashboard {
        .side-bar {
            max-width: unset;
            width: 50vw;
        }

        .dashboard-content {
            .dashboard-header {
                .breadcrumbs {
                    .breadcrumb-list {
                        display: none;
                    }
                }
            }
        }
    }
}