/* delete.css */
.delete-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    .delete-confirmation-popup {
        display: flex;
        gap: 12px;
        background: #fff;
        padding: 20px;
        border-radius: 8px;
        width: 18%;
        height: 18%;
        text-align: center;
        

      .image-text{
        display: flex;
        flex-direction: column;
        gap: 24px;
        h3{
            display: flex;
            flex-direction: start;
            font-size: 18px;
        }
        p{
            color: #999999;
            font-size: 14px;
        }

        .buttons {
            display: flex;
            justify-content: space-between;

            .primary-button,
            .buttons .secondary-button {
                width: 45%;
            }
        }
      }
    }
}