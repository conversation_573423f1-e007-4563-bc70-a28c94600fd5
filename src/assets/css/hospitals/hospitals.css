.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(24, 24, 24, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.hospital-content {
  background-color: white;
  padding: 24px;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.hospital-content .sidebar-logout {
  display: none;
}

button {
  cursor: pointer;
}

.hospital-filters {
  display: flex;
  gap: 20px;
  margin-left: auto;
  align-items: center;
}
.hospital-filters .hospital-year {
  display: flex;
  border: 1px solid #EDEDED;
  padding: 14px 20px;
  border-radius: 8px;
  gap: 8px;
}
.hospital-filters .hospital-export {
  display: flex;
  gap: 8px;
}

.hospitals-page {
  display: grid;
  gap: 1rem;
}
.hospitals-page .tabs {
  display: flex;
  gap: 1rem;
  cursor: pointer;
  background-color: white;
  border-radius: 0.5rem;
  padding-top: 24px;
}
.hospitals-page .tab {
  padding: 0.5rem 1rem;
  border-bottom: 2px solid transparent;
}
.hospitals-page .tab.active {
  border-bottom: 2px solid #07AEEF;
  font-weight: bold;
}
.hospitals-page .tab-contents {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: white;
  padding: 24px;
  border-radius: 0.5rem;
}
.hospitals-page .tab-contents .sidebar-logout {
  display: none;
}
.hospitals-page .tab-contents .dashboard-header {
  display: none;
}
.hospitals-page .tab-contents .content {
  background-color: white;
}
.hospitals-page .title {
  font-weight: bolder;
  font-size: 24px;
}
.hospitals-page .title .link {
  color: #07AEEF;
  font-weight: bolder;
  text-decoration: underline;
}

.hospital-bar-graph {
  display: flex;
  align-items: center;
  justify-content: start;
  flex-wrap: wrap;
  gap: 1rem;
}
.hospital-bar-graph .individual-graph {
  flex: 1 1 400px;
  border: 1px solid #E5E5E5;
  padding: 1rem;
  border-radius: 8px;
  width: 100%;
}
.hospital-bar-graph p {
  color: #999999;
  size: 16px;
}
.hospital-bar-graph h3 {
  font-size: 18px;
  font-weight: 700;
}

.popup-overlayer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: black;
}
.popup-overlayer .popup-content {
  background: #fff;
  padding: 30px;
  padding-top: 40px;
  border-radius: 8px;
  width: 528px;
  height: -moz-fit-content;
  height: fit-content;
  max-width: 90%;
  position: relative;
  display: grid;
  gap: 30px;
}
.popup-overlayer .popup-content .form-container {
  display: grid;
  gap: 1rem;
}
.popup-overlayer .popup-content .form-container p {
  padding: 20px;
  background-color: #ADF4CE;
  border-radius: 8px;
}
.popup-overlayer .popup-content .form-container .cancel-submit {
  display: flex;
  margin-left: auto;
  gap: 20px;
}
.popup-overlayer .popup-content .form-container .cancel-submit .cancel {
  background-color: transparent;
  border: 1px solid #E5E5E5;
  color: #999999;
  padding: 10px 35px;
  max-width: 148px;
  height: 44px;
  width: 100%;
  border-radius: 8px;
}
.popup-overlayer .popup-content .form-container .cancel-submit .save {
  background-color: #07AEEF;
  color: white;
  padding: 10px 35px;
  max-width: 148px;
  height: 44px;
  width: 100%;
  border-radius: 8px;
}
.popup-overlayer .popup-content .form-container h2 {
  font-size: 18px;
}

.success-message {
  display: grid;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 20px;
}
.success-message p {
  color: #999999;
}
.success-message button {
  background-color: #40C057;
  color: white;
  font-size: 16px;
}
.success-message .success-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.select-button {
  display: flex;
  justify-content: end;
  align-items: center;
  gap: 20px;
}

@media (max-width: 700px) {
  .hospital-filters {
    margin-left: 10px;
  }
}
@media (max-width: 540px) {
  .hospital-filters {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 414px) {
  .hospital-filters {
    display: grid;
    grid-template-columns: 1fr;
  }
}/*# sourceMappingURL=hospitals.css.map */