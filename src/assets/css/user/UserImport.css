.user-import-container {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
}
.user-import-container .import-button {
  padding: 8px 16px;
  background-color: #07AEEF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}
.user-import-container .import-button:hover {
  background-color: #0698d1;
}
.user-import-container .import-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.import-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.import-step {
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  position: relative;
}
.import-step .title {
  display: flex;
  justify-content: space-between;
  padding: 24px;
}
.import-step .import-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  align-items: center;
}
.import-step .import-content h3 {
  font-size: 16px;
  font-weight: 600;
}
.import-step .import-content p {
  color: #666;
  margin-top: 0;
  margin-bottom: 24px;
  font-size: 14px;
}
.import-step .drop-area {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  width: 100%;
}
.import-step .drop-area .browse-btn {
  background: none;
  border: none;
  color: #07AEEF;
  cursor: pointer;
  font-weight: 500;
  padding: 0;
}
.import-step .drop-area .browse-btn:hover {
  text-decoration: underline;
}
.import-step .upload-btn, .import-step .import-btn, .import-step .list-btn {
  width: 100%;
  padding: 12px;
  background-color: #07AEEF;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}
.import-step .upload-btn:hover, .import-step .import-btn:hover, .import-step .list-btn:hover {
  background-color: #0698d1;
}
.import-step .upload-btn:disabled, .import-step .import-btn:disabled, .import-step .list-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
.import-step .mapping-container {
  margin-bottom: 24px;
}
.import-step .mapping-container .mapping-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-weight: 500;
  font-size: 14px;
  color: #666;
}
.import-step .mapping-container .mapping-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.import-step .mapping-container .mapping-row .file-column, .import-step .mapping-container .mapping-row .property-column {
  flex: 1;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
.import-step .mapping-container .mapping-row .arrow {
  margin: 0 12px;
  color: #666;
}
.import-step .preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40px 0;
}
.import-step .preview-container .count-display {
  text-align: center;
}
.import-step .preview-container .count-display h1 {
  font-size: 48px;
  margin: 0;
  color: #333;
}
.import-step .preview-container .count-display p {
  margin: 8px 0 0;
  color: #666;
}
.import-step .action-buttons {
  display: flex;
  gap: 12px;
}
.import-step .action-buttons .back-btn {
  flex: 1;
  padding: 12px;
  background-color: white;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}
.import-step .action-buttons .back-btn:hover {
  background-color: #f5f5f5;
}
.import-step .action-buttons .import-btn {
  flex: 1;
}

.step-4 {
  padding: 40px;
  text-align: center;
}
.step-4 .success-icon {
  color: #4CAF50;
  margin-bottom: 16px;
}
.step-4 h2 {
  border: none;
  padding: 0;
  margin-bottom: 8px;
  font-size: 24px;
}
.step-4 p {
  color: #666;
  margin-bottom: 24px;
}
.step-4 .list-btn {
  background-color: #4CAF50;
}
.step-4 .list-btn:hover {
  background-color: #45a049;
}/*# sourceMappingURL=UserImport.css.map */