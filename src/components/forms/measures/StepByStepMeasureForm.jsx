import React, { useState, useRef, useEffect } from 'react';
import { TextInput, DataListInput, YearInput } from '../Input';
import { PrimaryButton, SecondaryButton } from '../buttons';
import toast from 'react-hot-toast';
import { Flag01Icon, ArrowLeft02Icon, ArrowRight02Icon } from "hugeicons-react";
import api, { API_URL } from '../../../api';
import Cookies from 'js-cookie';
import dayjs from 'dayjs';
import ConfirmPopup from '../ConfirmMeasurePopup';
import moment from 'moment/moment';
import '../../../assets/css/components/forms/step-by-step-measure-form.css';

const currentYear = dayjs();

const StepByStepMeasureForm = ({ isOpen, onClose, onMeasureAdded }) => {
    const [currentStep, setCurrentStep] = useState(1);
    const [name, setName] = useState('');
    const [synchronous_id, setSynchronous_id] = useState('');
    const [measure_year, setMeasureYear] = useState(currentYear);
    const [category, setCategory] = useState('');
    const [group, setGroup] = useState('');
    const [categories, setCategories] = useState([]);
    const [groups, setGroups] = useState([]);
    const [type, setType] = useState('');
    const [description, setDescription] = useState('');
    const [typeOptions] = useState(['numerical', 'percentage']);
    const [isSaving, setIsSaving] = useState(false);
    const [measureExistsMessage, setMeasureExistsMessage] = useState("");
    const [showConfirmPopup, setShowConfirmPopup] = useState(false);
    const [measureIdConfirmed, setMeasureIdConfirmed] = useState(false);
    const [isCheckingId, setIsCheckingId] = useState(false);
    const popupRef = useRef(null);

    const steps = [
        { number: 1, title: "Basic Information", subtitle: "Read Instructions Before Adding New Measure ID" },
        { number: 2, title: "Measure Descriptions", subtitle: "" },
        { number: 3, title: "Category & Type", subtitle: "" }
    ];

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

    useEffect(() => {
        if (isOpen) {
            setCurrentStep(1);
            setName('');
            setSynchronous_id('');
            setCategory('');
            setType('');
            setDescription('');
            setMeasureYear(currentYear);
            setMeasureExistsMessage("");
            setMeasureIdConfirmed(false);
        }
    }, [isOpen]);

    const fetchCategories = async () => {
        try {
            const response = await api.get(`${API_URL}/categories/list/`);
            const categoryNames = response.data.map(category => category.name);
            setCategories(categoryNames);
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.error('Failed to fetch categories');
        }
    };

    const fetchMeasureGroups = async () => {
        try {
            const response = await api.get(`${API_URL}/measures/measure-groups/`);
            const groups = response.data["measure_group_names"];
            setGroups(groups);
        } catch (error) {
            console.error('Error fetching groups:', error);
            toast.error('Failed to fetch groups');
        }
    };

    useEffect(() => {
        fetchCategories();
        fetchMeasureGroups();
    }, []);

    const checkMeasureIdExists = async (id) => {
        try {
            if (!id) return false;

            const token = Cookies.get('accessToken');
            const formattedYear = moment(measure_year).format('YYYY');
            const idStr = id.toString();

            const response = await api.get(`${API_URL}/measures/synchronous_id/`, {
                params: {
                    synchronous_id: idStr,
                    measure_year: formattedYear
                },
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (response.data.is_synchronous_id_exist) {
                if (response.data.same_year_exist) {
                    setMeasureExistsMessage(
                        response.data.same_year_message ||
                        `This Measure ID (${id}) is already in use for the current year. Please use a different ID.`
                    );
                    return false;
                } else {
                    return true;
                }
            } else {
                setMeasureExistsMessage("");
                return false;
            }
        } catch (error) {
            console.error('Error checking measure ID:', error);
            toast.error('Error checking if measure ID exists');
            return false;
        }
    };

    const [typingTimeout, setTypingTimeout] = useState(null);

    const handleSynchronousIdChange = (value) => {
        setSynchronous_id(value);
        setMeasureIdConfirmed(false);
        setMeasureExistsMessage("");
        setShowConfirmPopup(false);

        if (typingTimeout) {
            clearTimeout(typingTimeout);
        }

        if (value) {
            const newTimeout = setTimeout(async () => {
                try {
                    setIsCheckingId(true);
                    const exists = await checkMeasureIdExists(value);
                    if (exists) {
                        setMeasureExistsMessage(
                            `This Measure ID (${value}) has been used in previous reporting periods. Would you like to continue using it?`
                        );
                        setShowConfirmPopup(true);
                    } else {
                        setMeasureIdConfirmed(true);
                    }
                } catch (error) {
                    console.error("Error in handleSynchronousIdChange:", error);
                } finally {
                    setIsCheckingId(false);
                }
            }, 1000);

            setTypingTimeout(newTimeout);
        }
    };

    const handleConfirm = () => {
        setMeasureIdConfirmed(true);
        setShowConfirmPopup(false);
        setMeasureExistsMessage("");
    };

    const handleCancel = () => {
        setShowConfirmPopup(false);
        setSynchronous_id("");
        setMeasureExistsMessage("");
        setMeasureIdConfirmed(false);
    };

    const validateStep = (step) => {
        switch (step) {
            case 1:
                if (!name || !synchronous_id || !measure_year) {
                    toast.error('Please fill all required fields in this step');
                    return false;
                }
                if (!/^\d{6}$/.test(synchronous_id)) {
                    toast.error('Measure ID must be exactly 6 numeric digits');
                    return false;
                }
                if (showConfirmPopup) {
                    toast.error("Please confirm if you want to use this Measure ID");
                    return false;
                }
                return true;
            case 2:
                if (!description) {
                    toast.error('Please provide a measure description');
                    return false;
                }
                return true;
            case 3:
                if (!category || !type) {
                    toast.error('Please select category and type');
                    return false;
                }
                return true;
            default:
                return true;
        }
    };

    const nextStep = () => {
        if (validateStep(currentStep)) {
            setCurrentStep(prev => Math.min(prev + 1, 3));
        }
    };

    const prevStep = () => {
        setCurrentStep(prev => Math.max(prev - 1, 1));
    };

    const handleSubmit = async () => {
        if (!validateStep(3)) return;

        const transformYear = measure_year.format('YYYY');
        const newMeasure = {
            name,
            synchronous_id,
            category,
            description,
            group,
            "measure_unit": type,
            "measure_year": transformYear
        };

        setIsSaving(true);

        try {
            const token = Cookies.get('accessToken');
            const confirmedMeasure = {
                ...newMeasure,
                confirmed_id: measureIdConfirmed
            };

            const response = await api.post(`${API_URL}/measures/new_measure/`, confirmedMeasure, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 201) {
                toast.success('Measure added successfully');
                if (typeof onMeasureAdded === 'function') {
                    onMeasureAdded(response.data);
                }
                onClose();
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } catch (error) {
            console.error('Error adding measure:', error);
            if (error.response && error.response.data && error.response.data.error) {
                toast.error(error.response.data.error);
            } else {
                toast.error("An error occurred while saving.");
            }
        } finally {
            setIsSaving(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="step-measure-overlay">
            <div className="step-measure-form" ref={popupRef}>
                <div className="step-header">
                    <h2>Create New Measure</h2>
                    <div className="step-indicator">
                        {steps.map((step) => (
                            <div key={step.number} className={`step ${currentStep >= step.number ? 'active' : ''}`}>
                                <div className="step-number">{step.number}</div>
                                <div className="step-info">
                                    <div className="step-title">{step.title}</div>
                                    {step.subtitle && <div className="step-subtitle">{step.subtitle}</div>}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="step-content">
                    {currentStep === 1 && (
                        <div className="step-1">
                            <div className="measure-instructions">
                                <h3>Create New Measure Allowing Existing Measure ID is Not Allowed</h3>
                                <ul>
                                    <li>Create a unique measure ID. Reusing an existing measure ID is not allowed</li>
                                    <li>Numeric digits only</li>
                                    <li>Length must be six digits</li>
                                </ul>
                            </div>

                            {showConfirmPopup && (
                                <ConfirmPopup
                                    message={`The Measure ID "${synchronous_id}" is already in use from a previous reporting period. Would you like to continue using this ID?`}
                                    onConfirm={handleConfirm}
                                    onCancel={handleCancel}
                                    showConfirmPopup={showConfirmPopup}
                                />
                            )}

                            <TextInput
                                type='text'
                                name='measureName'
                                placeholder='Measure name'
                                value={name}
                                setValue={setName}
                            />

                            <div>
                                <label htmlFor="measureID">This field only accepts numerical values (6 digits required).</label>
                                <TextInput
                                    type="number"
                                    name="measureID"
                                    placeholder="Measure ID (6 digits)"
                                    value={synchronous_id}
                                    setValue={handleSynchronousIdChange}
                                    maxLength={6}
                                    pattern="[0-9]{6}"
                                />
                                {measureExistsMessage && (
                                    <p className="text-red">{measureExistsMessage}</p>
                                )}
                            </div>

                            <div onMouseDown={(e) => e.stopPropagation()} onClick={(e) => e.stopPropagation()}>
                                <YearInput
                                    year={measure_year}
                                    setYear={setMeasureYear}
                                />
                            </div>
                        </div>
                    )}

                    {currentStep === 2 && (
                        <div className="step-2">
                            <textarea
                                className='text-area-measure'
                                name='description'
                                placeholder='Measure Descriptions'
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                            ></textarea>
                        </div>
                    )}

                    {currentStep === 3 && (
                        <div className="step-3">
                            <div className='value-type'>
                                <DataListInput
                                    iconClass={<Flag01Icon />}
                                    id="category"
                                    name="categoryName"
                                    placeholder="Category Name"
                                    value={category}
                                    setValue={setCategory}
                                    options={categories}
                                />

                                <DataListInput
                                    iconClass={<Flag01Icon />}
                                    id="group"
                                    name="groupName"
                                    placeholder="Group name"
                                    value={group}
                                    setValue={setGroup}
                                    options={groups}
                                />

                                <DataListInput
                                    id="type"
                                    name="measureType"
                                    placeholder="Select Type"
                                    value={type}
                                    options={typeOptions}
                                    setValue={setType}
                                />
                            </div>
                        </div>
                    )}
                </div>

                <div className="step-actions">
                    <div className="left-actions">
                        {currentStep > 1 && (
                            <SecondaryButton
                                buttonText="Previous"
                                onClick={prevStep}
                                iconClass={<ArrowLeft02Icon />}
                            />
                        )}
                    </div>
                    
                    <div className="right-actions">
                        <SecondaryButton
                            buttonText="Cancel"
                            onClick={onClose}
                        />
                        
                        {currentStep < 3 ? (
                            <PrimaryButton
                                buttonText="Continue"
                                onClick={nextStep}
                                iconClass={<ArrowRight02Icon />}
                            />
                        ) : (
                            <PrimaryButton
                                buttonText="Save"
                                onClick={handleSubmit}
                                isLoading={isSaving}
                                processingText="Saving"
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StepByStepMeasureForm;
