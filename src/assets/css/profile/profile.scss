.user-profile {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: white;
  border-radius: 10px;
  padding: 24px;


  .top-actions {
    display: flex;
    justify-content: space-between;
  }

  .profile-info-container {
    gap: 25px;
    display: flex;
    flex-direction: row;
    align-items: center;


    .user-details {
      width: 30%;
      border-radius: 8px;
      border: 1px solid #E5E5E5;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px;
      gap: 10px;

      .user-image {

        img {
          height: 3rem;
          width: 3rem;
          padding: 0.2rem;
          border-radius: 50%;
          border: 1px solid #E5E5E5;
          background-color: white;
          object-fit: contain;
        }
      }

      .personal {
        padding: 15px;
        height: 111px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;

        h2 {
          margin: 0;
        }

        p {
          margin: 5px 0;
        }

        .position,
        .id {
          color: #999999;
        }

        .vertical-container {
          display: flex;
          justify-content: center;
          align-items: center;

          .vertical-line {
            width: 2px;
            height: 27px;
            background-color: #E5E5E5;
          }
        }
      }
    }

    .view-information {
      border-radius: 8px;
      display: flex;
      width: 70%;
      border: 1px solid #ddd;
      padding: 20px;
      gap: 24px;

      p {
        margin: 5px 0;
      }

      strong {
        color: #999999;
      }

      .user-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 100px;
        padding: 6px;

        .individual-user {
          display: grid;
          gap: 18px;

          .text {
            color: #999999;
          }

          hr {
            border: none;
            height: 2px;
            background-color: #F2F2F2;
          }
        }
      }
    }
  }

  .popup-overlayer {
    .form-container {
      .fields-row {
        display: grid;
        gap: 20px;

        .selectposition-id {
          display: flex;
          gap: 20px;
          font-weight: bold;

          label {
            width: 100%;

            input,
            select {
              width: 100%;
              padding: 8px;
              border: 1px solid #ddd;
              border-radius: 4px;
            }
          }
        }
      }
    }

    .popup-form {
      .singler-fild {
        display: flex;
        gap: 30px;
      }

      .next-cancel {
        display: flex;
        justify-content: space-between;

        button {
          padding: 10px 20px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
          color: #fff;
        }

        button:hover {
          opacity: 0.8;
        }
      }
    }

    .popup-content2 {
      display: grid;
      gap: 1rem;

      .hospital-lastname {
        display: flex;
        gap: 20px;
        font-weight: bold;

        label {
          width: 100%;

          input,
          select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
          }
        }
      }

      .single-field {
        label {
          width: 100%;

          input,
          select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
          }
        }


      }

      .cancel-submit {
        display: flex;
        justify-content: flex-end;
        gap: 20px;

        .cancel {
          background-color: transparent;
          border: 1px solid #E5E5E5;
          color: #999999;
          padding: 10px 35px;
          border-radius: 8px;
        }

        .submit {
          background-color: #07AEEF;
          color: white;
          padding: 10px 35px;
          border-radius: 8px;
        }
      }

      .date-field {
        input {
          // width: 100%;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }
      }
    }
  }

  @media (max-width: 880px) {
    .profile-info-container {
      flex-direction: column;
      gap: 20px;

      .user-details {
        width: 100%;
      }

      .view-information {
        gap: 50px;
        flex-direction: column;
      }
    }
  }

  @media (max-width: 768px) {
    .profile-info-container {
      gap: 15px;



      .view-information {
        gap: 30px;
        padding: 15px;
        flex-direction: column;
      }

      .user-info {
        grid-template-columns: 1fr 1fr;
        gap: 50px;
      }
    }

    .popup-overlayer {
      .popup-content2 {
        .hospital-lastname {
          flex-direction: column;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .profile-info-container {
      gap: 10px;

      .user-details {
        padding: 15px;
      }

      .view-information {
        padding: 10px;
        gap: 20px;
      }

      .user-info {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }

    .popup-overlayer {
      .popup-content2 {

        .hospital-lastname,
        .single-field {
          label {

            input,
            select {
              padding: 6px;
            }
          }
        }
      }
    }
  }
}