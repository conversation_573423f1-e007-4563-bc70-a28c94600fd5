.measure-action-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.measure-action-dialog {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}
.measure-action-dialog h2 {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}
.measure-action-dialog > p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 24px 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.action-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}
.action-option:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.option-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.option-icon.create-icon {
  background-color: #dcfce7;
  color: #16a34a;
}
.option-icon.copy-icon {
  background-color: #dbeafe;
  color: #2563eb;
}

.option-content {
  flex: 1;
}
.option-content h3 {
  color: #1f2937;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 2px 0;
}
.option-content p {
  color: #6b7280;
  font-size: 12px;
  margin: 0;
}

.dialog-actions {
  display: flex;
  justify-content: center;
  padding-top: 20px;
}

@media (max-width: 600px) {
  .measure-action-dialog {
    padding: 20px;
  }
  .action-option {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  .option-content {
    text-align: center;
  }
}/*# sourceMappingURL=measure-action-dialog.css.map */