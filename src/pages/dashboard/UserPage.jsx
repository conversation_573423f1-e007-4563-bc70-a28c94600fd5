import React, { useState, useEffect } from 'react';
import api, { API_URL } from '../../api';

import '../../assets/css/user/user.css';
import { PrimaryButton } from '../../components/forms/buttons';
import {PlusSignIcon, FileExportIcon, ArrowDown01Icon, Edit02Icon, Delete01Icon, Search01Icon, CleanIcon } from 'hugeicons-react';
import { jsPDF } from "jspdf";
import AddUserForm from '../../components/forms/AddUserForm';
import { useNavigate } from 'react-router-dom';
import DashboardContainer from '../../components/dashboard/DashboardContainer';
import { DataListInput, TextInput } from '../../components/forms/Input'
import EditUser from '../../components/user/EditUser';
import DeleteUser from '../../components/user/DeleteUser';
import Cookies from 'js-cookie';
import 'react-toastify/dist/ReactToastify.css';
import UserImport from '../../components/user/UserImport';
import UserRolesPage from '../dashboard/UserRolesPage';
import { hasRole } from '../../services/userPosition';
import StandardSearchBar from '../../components/common/StandardSearchBar';
import TableLoading from '../../components/loading/TableLoading';

const UserPageContent = () => {
    const [users, setUsers] = useState([]);
    const [isPopupOpen, setIsPopupOpen] = useState(false);
    const [isOverlayVisible, setIsOverlayVisible] = useState(false);
    const [isSubmitting] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [isEditPopupOpen, setIsEditPopupOpen] = useState(false);
    const [isDeletePopupOpen, setIsDeletePopupOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const [roles, setRoles] = useState(["Roles"]);
    const [selectedRoles, setSelectedRoles] = useState('');
    const [selectedUserData, setSelectedUserData] = useState(null);
    const [activeTab, setActiveTab] = useState('users');
    const [selectedUserId, setSelectedUserId] = useState(null);

    const navigate = useNavigate();
const handleUserAdded = (newUser) => {
    setUsers((prevUsers) => [...prevUsers, newUser]);
};

const handleUsersImported = () => {
    fetchUsers();
};


    const handleUserUpdated = (updatedUser) => {
        setUsers((prevUsers) =>
            prevUsers.map((user) => (user.id === updatedUser.id ? updatedUser : user))
        );
    };

    const handleUserDeleted = (userId) => {
        setUsers((prevUsers) => prevUsers.filter((user) => user.id !== userId));
    };


    const fetchUsers = async () => {
        setIsOverlayVisible(true);
        try {
            const response = await api.get(`${API_URL}/user/list_users/`);
            if (response.status === 200) {
                setUsers(response.data);

                const uniquePermissions = [
                    ...new Set(
                        response.data.map(user => user.permission)
                    )
                ];
                setRoles(uniquePermissions);
            } else {
                console.error('Error: Response status is not 200');
            }
        } catch (error) {
            if (!navigator.onLine) {
                console.error("You are offline. Please check your internet connection.");
                return { error: "offline" };
            }

            console.error('Error fetching users:', error);
        } finally {
            setIsOverlayVisible(false);
        }
    };

    useEffect(() => {
        fetchUsers();
    }, []);

    const handleAddNewUser = () => {
        setIsPopupOpen(true);
    };

    const closePopup = () => {
        setIsPopupOpen(false);
        setIsEditPopupOpen(false);
        setIsDeletePopupOpen(false);
        fetchUsers();
    };


    const handleRowClicks = (user_id) => {
        navigate(`/users/${user_id}`);
    };


    const handleDeleteClick = (user) => {
        setSelectedUser(user);
        setIsDeletePopupOpen(true);
    };

    const handleEditUser = (userId) => {
        const user = users.find((user) => user.user_id === userId);
        setSelectedUserId(userId);
        setSelectedUserData(user);
        setSelectedUserData(user);
        setIsEditPopupOpen(true);
    };


    const handleRolesChange = (role) => {
        setSelectedRoles(role);
    };

    const handleResetFilters = () => {
        setSelectedRoles('');
        setSearchQuery('');
    };

    const filteredUsers = users.filter(user => {
        const lowerCaseUsername = user?.username?.toLowerCase() || '';
        const lowerCaseFirstName = user?.first_name?.toLowerCase() || '';
        const lowerCaseLastName = user?.last_name?.toLowerCase() || '';
        const lowerCaseEmail = user?.email?.toLowerCase() || '';
        const lowerCaseDateCreated = user?.date_added?.toLowerCase() || '';
        const lowerCaseRoles = user?.position?.toLowerCase() || '';
        const lowerCaseQuery = searchQuery.toLowerCase();
        return (
            lowerCaseUsername.includes(lowerCaseQuery) ||
            lowerCaseFirstName.includes(lowerCaseQuery) ||
            lowerCaseLastName.includes(lowerCaseQuery) ||
            lowerCaseEmail.includes(lowerCaseQuery) ||
            lowerCaseDateCreated.includes(lowerCaseQuery))
            && (selectedRoles === '' || lowerCaseRoles.includes(selectedRoles.toLowerCase()))
    });


    const handleExport = () => {
        const doc = new jsPDF();
        const tableColumn = ["First Name", "Last Name", "Username", "Email", "Hospital/Facility", "User Role"];
        const tableRows = [];
        filteredUsers.forEach(user => {
            const userData = [
                user.first_name,
                user.last_name,
                user.username,
                user.email,
                user.hospital || 'Not Assigned',
                user.position,
            ];
            tableRows.push(userData);
        });
        doc.autoTable({
            head: [tableColumn],
            body: tableRows,
        });
        doc.save("users.pdf");
    };


    return (
        <div className="user-content">
            {isPopupOpen && (
                <div className="overlay">
                    <AddUserForm
                        isOpen={isPopupOpen}
                        onClose={closePopup}
                        onUserAdded={handleUserAdded}
                    />
                </div>
            )}

<div className="buttons-search">
<div className="search-users">
                <StandardSearchBar
                    placeholder="Search by username"
                    value={searchQuery}
                    onChange={setSearchQuery}
                    className="compact"
                />
            </div>
<div className="add-user-bttns">
<div className="export-bttn" onClick={handleExport}>
                    <FileExportIcon size={24} />
                    <p>Export Users</p>
                </div>
                {
                    hasRole(["Admin","User Editor","Super User"])
                ?
                (
                    <div className="primary-btn">
                    <PrimaryButton

                        isLoading={isSubmitting}
                        onClick={handleAddNewUser}
                        processingText={'Submitting'}
                        iconClass={<PlusSignIcon />}
                        buttonText={'Add New User'}
                    />
                    </div>
                )
                : <></>}
               </div>
               <UserImport onUsersImported={handleUsersImported} />
               </div>


            <div className="tabs">
                <div
                    className={`tab ${activeTab === 'users' ? 'active' : ''}`}
                    onClick={() => setActiveTab('users')}
                >
                    All Users
                </div>

            </div>

            {activeTab === 'users' && (
                <div className="table-container">
                    {isOverlayVisible && <TableLoading />}
                    {filteredUsers.length > 0 ? (
                        <table>
                            <thead>
                                <tr>

                                    <th>ID</th>
                                    <th>Username</th>
                                    <th>Names</th>
                                    <th>Hospital/Facility</th>
                                    <th>
                                        <div className="role-filter-container">
                                            <strong>
                                                <DataListInput
                                                    id="permission"
                                                    name="permission"
                                                    placeholder="Roles"
                                                    iconClass={<ArrowDown01Icon />}
                                                    options={roles}
                                                    value={selectedRoles}
                                                    setValue={handleRolesChange}
                                                />
                                            </strong>
                                            {selectedRoles && (
                                                <button
                                                    className="reset-filter-btn"
                                                    onClick={handleResetFilters}
                                                    title="Clear filters"
                                                >
                                                    <CleanIcon size={16} />
                                                </button>
                                            )}
                                        </div>
                                    </th>
                                    {
                                        hasRole(["Admin","User Editor","Super User"])? <th>Action</th> : <></>

                                    }


                                </tr>
                            </thead>
                            <tbody>
                                {isEditPopupOpen && (
                                    <EditUser userId={selectedUserId} userData={selectedUserData} onClose={closePopup} onSubmit={handleUserUpdated} />

                                )}
                                {isDeletePopupOpen && (
                                    <DeleteUser
                                        isOpen={isDeletePopupOpen}
                                        userId={selectedUser}
                                        onClose={closePopup}
                                        deleteUser={handleUserDeleted}
                                    />
                                )}
                                {filteredUsers.map((user) => (
                                    <React.Fragment key={user.user_id}>
                                        <tr
                                            key={user.user_id}
                                            style={{ cursor: 'pointer' }}
                                            onClick={() => handleRowClicks(user.user_id)}
                                        >

                                            <td>{user.account_id}</td>
                                            <td className='td-img'>
                                                <img src='/logo.png' alt="" />
                                                {user.username || 'N/A'}
                                            </td>
                                            <td>{user.first_name} {user.last_name}</td>
                                            <td>{user.hospital || 'Not Assigned'}</td>
                                            <td>{user.position}</td>
                                            {hasRole(["Admin","User Editor","Super User"]) ?
                                                <td>
                                                    <div className="action-buttons">
                                                        <Edit02Icon
                                                        size={32}
                                                            className="edit-icon"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleEditUser(user.user_id);
                                                            }}
                                                        />
                                                        <Delete01Icon
                                                         size={32}
                                                            className="delete-icon"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleDeleteClick(user.user_id);
                                                            }}
                                                        />
                                                    </div>
                                                </td>
                                                :
                                                <></>

                                            }

                                        </tr>

                                    </React.Fragment>
                                ))}
                            </tbody>
                        </table>
                    ) : (
                        <p>No users found.</p>
                    )}
                </div>
            )}

            {activeTab === 'userRoles' && (
                <div>

                    <UserRolesPage />
                </div>
            )}
        </div>
    );
};

const UserPage = () => {
    return (
        <DashboardContainer content={<UserPageContent />} pageTitle={'Users'} />
    );
};

export default UserPage;


