.user-profile {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: white;
  border-radius: 10px;
  padding: 24px;
}
.user-profile .top-actions {
  display: flex;
  justify-content: space-between;
}
.user-profile .profile-info-container {
  gap: 25px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.user-profile .profile-info-container .user-details {
  width: 30%;
  border-radius: 8px;
  border: 1px solid #E5E5E5;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  gap: 10px;
}
.user-profile .profile-info-container .user-details .user-image img {
  height: 3rem;
  width: 3rem;
  padding: 0.2rem;
  border-radius: 50%;
  border: 1px solid #E5E5E5;
  background-color: white;
  -o-object-fit: contain;
     object-fit: contain;
}
.user-profile .profile-info-container .user-details .personal {
  padding: 15px;
  height: 111px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}
.user-profile .profile-info-container .user-details .personal h2 {
  margin: 0;
}
.user-profile .profile-info-container .user-details .personal p {
  margin: 5px 0;
}
.user-profile .profile-info-container .user-details .personal .position,
.user-profile .profile-info-container .user-details .personal .id {
  color: #999999;
}
.user-profile .profile-info-container .user-details .personal .vertical-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
.user-profile .profile-info-container .user-details .personal .vertical-container .vertical-line {
  width: 2px;
  height: 27px;
  background-color: #E5E5E5;
}
.user-profile .profile-info-container .view-information {
  border-radius: 8px;
  display: flex;
  width: 70%;
  border: 1px solid #ddd;
  padding: 20px;
  gap: 24px;
}
.user-profile .profile-info-container .view-information p {
  margin: 5px 0;
}
.user-profile .profile-info-container .view-information strong {
  color: #999999;
}
.user-profile .profile-info-container .view-information .user-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 100px;
  padding: 6px;
}
.user-profile .profile-info-container .view-information .user-info .individual-user {
  display: grid;
  gap: 18px;
}
.user-profile .profile-info-container .view-information .user-info .individual-user .text {
  color: #999999;
}
.user-profile .profile-info-container .view-information .user-info .individual-user hr {
  border: none;
  height: 2px;
  background-color: #F2F2F2;
}
.user-profile .popup-overlayer .form-container .fields-row {
  display: grid;
  gap: 20px;
}
.user-profile .popup-overlayer .form-container .fields-row .selectposition-id {
  display: flex;
  gap: 20px;
  font-weight: bold;
}
.user-profile .popup-overlayer .form-container .fields-row .selectposition-id label {
  width: 100%;
}
.user-profile .popup-overlayer .form-container .fields-row .selectposition-id label input,
.user-profile .popup-overlayer .form-container .fields-row .selectposition-id label select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.user-profile .popup-overlayer .popup-form .singler-fild {
  display: flex;
  gap: 30px;
}
.user-profile .popup-overlayer .popup-form .next-cancel {
  display: flex;
  justify-content: space-between;
}
.user-profile .popup-overlayer .popup-form .next-cancel button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  color: #fff;
}
.user-profile .popup-overlayer .popup-form .next-cancel button:hover {
  opacity: 0.8;
}
.user-profile .popup-overlayer .popup-content2 {
  display: grid;
  gap: 1rem;
}
.user-profile .popup-overlayer .popup-content2 .hospital-lastname {
  display: flex;
  gap: 20px;
  font-weight: bold;
}
.user-profile .popup-overlayer .popup-content2 .hospital-lastname label {
  width: 100%;
}
.user-profile .popup-overlayer .popup-content2 .hospital-lastname label input,
.user-profile .popup-overlayer .popup-content2 .hospital-lastname label select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.user-profile .popup-overlayer .popup-content2 .single-field label {
  width: 100%;
}
.user-profile .popup-overlayer .popup-content2 .single-field label input,
.user-profile .popup-overlayer .popup-content2 .single-field label select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.user-profile .popup-overlayer .popup-content2 .cancel-submit {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
}
.user-profile .popup-overlayer .popup-content2 .cancel-submit .cancel {
  background-color: transparent;
  border: 1px solid #E5E5E5;
  color: #999999;
  padding: 10px 35px;
  border-radius: 8px;
}
.user-profile .popup-overlayer .popup-content2 .cancel-submit .submit {
  background-color: #07AEEF;
  color: white;
  padding: 10px 35px;
  border-radius: 8px;
}
.user-profile .popup-overlayer .popup-content2 .date-field input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
@media (max-width: 880px) {
  .user-profile .profile-info-container {
    flex-direction: column;
    gap: 20px;
  }
  .user-profile .profile-info-container .user-details {
    width: 100%;
  }
  .user-profile .profile-info-container .view-information {
    gap: 50px;
    flex-direction: column;
  }
}
@media (max-width: 768px) {
  .user-profile .profile-info-container {
    gap: 15px;
  }
  .user-profile .profile-info-container .view-information {
    gap: 30px;
    padding: 15px;
    flex-direction: column;
  }
  .user-profile .profile-info-container .user-info {
    grid-template-columns: 1fr 1fr;
    gap: 50px;
  }
  .user-profile .popup-overlayer .popup-content2 .hospital-lastname {
    flex-direction: column;
  }
}
@media (max-width: 480px) {
  .user-profile .profile-info-container {
    gap: 10px;
  }
  .user-profile .profile-info-container .user-details {
    padding: 15px;
  }
  .user-profile .profile-info-container .view-information {
    padding: 10px;
    gap: 20px;
  }
  .user-profile .profile-info-container .user-info {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .user-profile .popup-overlayer .popup-content2 .hospital-lastname label input,
  .user-profile .popup-overlayer .popup-content2 .hospital-lastname label select,
  .user-profile .popup-overlayer .popup-content2 .single-field label input,
  .user-profile .popup-overlayer .popup-content2 .single-field label select {
    padding: 6px;
  }
}/*# sourceMappingURL=profile.css.map */