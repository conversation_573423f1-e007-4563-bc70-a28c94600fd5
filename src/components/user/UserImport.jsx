import React, { useState, useRef, useEffect } from 'react';
import api, { API_URL } from '../../api';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';
import { Upload03Icon, Cancel01Icon, CheckCircleIcon } from 'hugeicons-react';
import '../../assets/css/user/UserImport.css';


const UserImport = ({ onUsersImported }) => {
    const [file, setFile] = useState(null);
    const [isUploading, setIsUploading] = useState(false);
    const [currentStep, setCurrentStep] = useState(1);
    const [mappedFields, setMappedFields] = useState({
        email: 'Email',
        first_name: 'First Name',
        last_name: 'Last Name',
        position: 'Position',
        hospital: 'Hospital'
    });
    const [previewData, setPreviewData] = useState(null);
    const [importedCount, setImportedCount] = useState(0);
    const [showModal, setShowModal] = useState(false);
    
    const fileInputRef = useRef(null);
    const dropAreaRef = useRef(null);

    const handleFileChange = (e) => {
        const selectedFile = e.target.files[0];
        if (selectedFile) {
            setFile(selectedFile);
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        if (dropAreaRef.current) {
            dropAreaRef.current.classList.add('drag-over');
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        if (dropAreaRef.current) {
            dropAreaRef.current.classList.remove('drag-over');
        }
    };

    const handleDrop = (e) => {
        e.preventDefault();
        if (dropAreaRef.current) {
            dropAreaRef.current.classList.remove('drag-over');
        }
        
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            setFile(e.dataTransfer.files[0]);
        }
    };

    const handleFieldMapping = (sourceField, targetField) => {
        setMappedFields(prev => ({
            ...prev,
            [targetField]: sourceField
        }));
    };

    const handleUpload = async () => {
        if (!file) {
            toast.error('Please select a file');
            return;
        }

        setIsUploading(true);

        try {
            const formData = new FormData();
            formData.append('file', file);
            
            // Add field mappings to the request
            Object.entries(mappedFields).forEach(([key, value]) => {
                formData.append(`mapping[${key}]`, value);
            });

            const token = Cookies.get('accessToken');
            
            // First request to validate and get preview
            const previewResponse = await api.post(`${API_URL}/user/preview_import/`, formData, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (previewResponse.status === 200) {
                setPreviewData(previewResponse.data);
                setImportedCount(previewResponse.data.length || 84); // Example count
                setCurrentStep(3);
            }
        } catch (error) {
            console.error('Error previewing import:', error);
            toast.error('Error previewing import');
        } finally {
            setIsUploading(false);
        }
    };

    const confirmImport = async () => {
        setIsUploading(true);

        try {
            const formData = new FormData();
            formData.append('file', file);
            
            // Add field mappings to the request
            Object.entries(mappedFields).forEach(([key, value]) => {
                formData.append(`mapping[${key}]`, value);
            });
            
            formData.append('confirm', 'true');

            const token = Cookies.get('accessToken');
            const response = await api.post(`${API_URL}/user/import_users/`, formData, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (response.status === 200) {
                setCurrentStep(4);
                if (typeof onUsersImported === 'function') {
                    onUsersImported(response.data);
                }
            } else {
                toast.error('Failed to import users');
            }
        } catch (error) {
            console.error('Error importing users:', error);
            toast.error('Error importing users');
        } finally {
            setIsUploading(false);
        }
    };

    const resetImport = () => {
        setFile(null);
        setCurrentStep(1);
        setPreviewData(null);
        setImportedCount(0);
        setShowModal(false);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const closeModal = () => {
        resetImport();
    };

    const goToList = () => {
        resetImport();
        // Navigate back to user list or close modal
        window.location.href = '/users-list';
    };

    const handleImportClick = () => {
        // Show the modal immediately when Import Users is clicked
        setShowModal(true);
    };

    const handleBrowseClick = (e) => {
        e.stopPropagation();
        // Open the file dialog safely
        if (fileInputRef.current) {
            fileInputRef.current.click();
        } else {
            console.error("File input reference is null");
            toast.error("Couldn't open file dialog. Please try again.");
        }
    };

    // Render different steps based on currentStep
    const renderStep = () => {
        switch (currentStep) {
            case 1: // Initial upload screen
                return (
                    <div className="import-step step-1">
                        <div className='title'>
                             <h2>Import users</h2>
                        <div className="close-btn" onClick={closeModal}>
                            <Cancel01Icon size={20} />
                        </div>
                        </div>
                       
                        
                        <div className="import-content">
                            <h3>Import csv file</h3>
                            <p>Make sure csv include name and dates</p>
                            
                            <div 
                                className="drop-area" 
                                ref={dropAreaRef}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                                onDrop={handleDrop}
                                onClick={handleBrowseClick}
                            >
                                <Upload03Icon size={32} color='#000000' />
                                <p>Drag or drop to upload</p>
                                <p onClick={handleBrowseClick} className='browse-btn'>or browse files </p>
                                <input 
                                    type="file" 
                                    ref={fileInputRef}
                                    accept=".csv, .xlsx" 
                                    onChange={handleFileChange} 
                                    style={{ display: 'none' }}
                                />
                            </div>
                            
                            <button 
                                className="upload-btn"
                                onClick={() => file ? setCurrentStep(2) : handleBrowseClick()}
                                disabled={isUploading}
                            >
                                {isUploading ? 'Uploading...' : 'Upload users'}
                            </button>
                        </div>
                    </div>
                );
                
            case 2: // Field mapping screen
                return (
                    <div className="import-step step-2">
                        <h2>Import users</h2>
                        <div className="close-btn" onClick={closeModal}>
                            <Cancel01Icon size={20} />
                        </div>
                        
                        <div className="import-content">
                            <h3>Users</h3>
                            <p>These properties will be used to identify your users</p>
                            
                            <div className="mapping-container">
                                <div className="mapping-header">
                                    <span>Columns in your file</span>
                                    <span>Properties on user list</span>
                                </div>
                                
                                {Object.entries(mappedFields).map(([key, value]) => (
                                    <div className="mapping-row" key={key}>
                                        <div className="file-column">{value}</div>
                                        <div className="arrow">→</div>
                                        <div className="property-column">{key}</div>
                                    </div>
                                ))}
                            </div>
                            
                            <button 
                                className="upload-btn"
                                onClick={handleUpload}
                                disabled={isUploading}
                            >
                                {isUploading ? 'Processing...' : 'Upload users'}
                            </button>
                        </div>
                    </div>
                );
                
            case 3: // Preview/confirmation screen
                return (
                    <div className="import-step step-3">
                        <h2>Import users</h2>
                        <div className="close-btn" onClick={closeModal}>
                            <Cancel01Icon size={20} />
                        </div>
                        
                        <div className="import-content">
                            <h3>Users</h3>
                            <p>Few details before you input users</p>
                            
                            <div className="preview-container">
                                <div className="count-display">
                                    <h1>{importedCount}</h1>
                                    <p>Users created</p>
                                </div>
                            </div>
                            
                            <div className="action-buttons">
                                <button 
                                    className="back-btn"
                                    onClick={() => setCurrentStep(2)}
                                >
                                    Back
                                </button>
                                <button 
                                    className="import-btn"
                                    onClick={confirmImport}
                                    disabled={isUploading}
                                >
                                    {isUploading ? 'Importing...' : 'Import Users'}
                                </button>
                            </div>
                        </div>
                    </div>
                );
                
            case 4: // Success screen
                return (
                    <div className="import-step step-4">
                        <div className="success-content">
                            <div className="success-icon">
                                {/* <CheckCircleIcon size={48} /> */}
                            </div>
                            <h2>Successfully</h2>
                            <p>You have successfully imported new users</p>
                            
                            <button 
                                className="list-btn"
                                onClick={goToList}
                            >
                                Back to List
                            </button>
                        </div>
                    </div>
                );
                
            default:
                return null;
        }
    };

    // Render the import button or the current step
    return (
        <div className="user-import-container">
            <input 
                type="file" 
                ref={fileInputRef}
                accept=".csv, .xlsx" 
                onChange={handleFileChange} 
                style={{ display: 'none' }}
            />
            
            <button 
                className="import-button" 
                onClick={handleImportClick}
                disabled={isUploading}
            >
                <Upload03Icon size={20} />
                Import Users
            </button>
            
            {showModal && (
                <div className="import-modal-overlay">
                    <div className="import-modal">
                        {renderStep()}
                    </div>
                </div>
            )}
        </div>
    );
};

export default UserImport;
