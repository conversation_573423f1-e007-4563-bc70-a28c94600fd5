import React, { useState } from 'react'
import SideBarDoc from './SideBarDoc'
import DashboardHeader from './DashboardHeader'

const DashboardContainerDoc = ({ content, pageTitle }) => {
    const [mobileMenuOpen, setMobileMenuOpen] = useState(true)
    return (
        <div className='dashboard'>
            <SideBarDoc mobileMenuOpen={mobileMenuOpen} setMobileMenuOpen={setMobileMenuOpen} />
            <div className="dashboard-content">
                <DashboardHeader mobileMenuOpen={mobileMenuOpen} setMobileMenuOpen={setMobileMenuOpen} pageTitle={pageTitle} />
                <div className="content">
                    {content}
                </div>
            </div>
        </div>
    )
}



export default DashboardContainerDoc
