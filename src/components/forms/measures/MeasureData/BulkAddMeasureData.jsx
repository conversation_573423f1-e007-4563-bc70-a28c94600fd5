import { useState, useEffect, useRef } from 'react';
import { DateInput } from '../../Input';
import { PrimaryButton, SecondaryButton } from '../../buttons';
import toast from 'react-hot-toast';
import { Delete01Icon, PlusSignIcon, Copy01Icon } from "hugeicons-react";
import api, { API_URL } from '../../../../api';
import '../../../../assets/css/components/forms/bulk-measure-data.css';

const BulkAddMeasureData = ({ onClose, onAddSuccess }) => {
    const [startingDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [dateError, setDateError] = useState('');
    const [isDateValid, setIsDateValid] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [hasYearData, setHasYearData] = useState(true);
    const [measures, setMeasures] = useState([]);
    const [hospitals, setHospitals] = useState([]);
    const [measureUnits, setMeasureUnits] = useState({});
    const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

    // Array to hold multiple measure data entries
    const [measureDataEntries, setMeasureDataEntries] = useState([
        { id: 1, measure: '', hospital: '', value: '', measureUnit: 'numerical' }
    ]);

    const popupRef = useRef(null);

    const formatDate = (dateObj) => {
        if (!dateObj) return null;

        try {
            // Handle dayjs object
            if (dateObj.$y && dateObj.$M !== undefined && dateObj.$D) {
                const year = dateObj.$y;
                const month = (dateObj.$M + 1).toString().padStart(2, '0');
                const day = dateObj.$D.toString().padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // Handle Date object or string
            const date = new Date(dateObj);
            if (isNaN(date.getTime())) {
                console.error('Invalid date:', dateObj);
                return null;
            }

            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        } catch (error) {
            console.error('Error formatting date:', error);
            return null;
        }
    };

    // Add keyboard support for closing the modal
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === 'Escape' && !isDatePickerOpen) {
                onClose();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [onClose, isDatePickerOpen]);

    const fetchMeasures = async (year) => {
        try {
            const response = await api.get(`${API_URL}/measures/`, {
                params: { measure_year: year }
            });
            const measuresArray = response.data.map(item => item.name);
            setMeasures(measuresArray);
            setHasYearData(measuresArray.length > 0);
        } catch (error) {
            console.error('Error fetching measures:', error);
            toast.error('Failed to load measures');
        }
    };

    const fetchMeasureUnit = async (measureName, year) => {
        try {
            if (!measureName || !year) return 'numerical';

            const response = await api.get(`${API_URL}/measures/measure_unit/`, {
                params: {
                    measure_name: measureName,
                    measure_year: year
                }
            });

            return response.data?.type || 'numerical';
        } catch (error) {
            console.error('Error fetching measure unit:', error);
            return 'numerical';
        }
    };

    useEffect(() => {
        const fetchHospitals = async () => {
            try {
                const response = await api.get(`${API_URL}/hospitals/`);

                // Handle the new API response format with results array
                let hospitalsData = [];
                if (response.data && response.data.results && Array.isArray(response.data.results)) {
                    hospitalsData = response.data.results;
                } else if (Array.isArray(response.data)) {
                    // Fallback for direct array format
                    hospitalsData = response.data;
                }

                const hospitalArray = hospitalsData.map(item => item.name);
                setHospitals(hospitalArray);
            } catch (error) {
                toast.error('Failed to load hospitals');
            }
        };
        fetchHospitals();
    }, []);

    useEffect(() => {
        // Only validate if both dates are selected
        if (!startingDate || !endDate) {
            setIsDateValid(false);
            return;
        }

        try {
            if (startingDate.$y === undefined || endDate.$y === undefined) {
                setIsDateValid(false);
                return;
            }

            if (startingDate.$y !== endDate.$y) {
                setIsDateValid(false);
                setDateError("Both Starting Date and Ending date should be in the same year.");
                return;
            }

            if (startingDate.$m > endDate.$m || (startingDate.$m === endDate.$m && startingDate.$d > endDate.$d)) {
                setIsDateValid(false);
                setDateError("Ending date should be greater than Starting Date.");
                return;
            }

            const year = startingDate.$y;
            fetchMeasures(year);
            setIsDateValid(true);
            setDateError('');
        } catch (error) {
            console.error('Error validating dates:', error.message);
            setIsDateValid(false);
        }
    }, [startingDate, endDate]);

    const addNewEntry = () => {
        const newId = Math.max(...measureDataEntries.map(entry => entry.id)) + 1;
        setMeasureDataEntries([
            ...measureDataEntries,
            { id: newId, measure: '', hospital: '', value: '', measureUnit: 'numerical' }
        ]);
    };

    const removeEntry = (id) => {
        if (measureDataEntries.length > 1) {
            setMeasureDataEntries(measureDataEntries.filter(entry => entry.id !== id));
        }
    };

    const duplicateEntry = (id) => {
        const entryToDuplicate = measureDataEntries.find(entry => entry.id === id);
        if (entryToDuplicate) {
            const newId = Math.max(...measureDataEntries.map(entry => entry.id)) + 1;
            const duplicatedEntry = {
                ...entryToDuplicate,
                id: newId,
                value: '' 
            };
            setMeasureDataEntries([...measureDataEntries, duplicatedEntry]);
        }
    };

    const updateEntry = async (id, field, value) => {
        const updatedEntries = measureDataEntries.map(entry => {
            if (entry.id === id) {
                const updatedEntry = { ...entry, [field]: value };
                
                if (field === 'measure' && value && startingDate?.$y) {
                    fetchMeasureUnit(value, startingDate.$y).then(unit => {
                        setMeasureDataEntries(prev => 
                            prev.map(e => 
                                e.id === id ? { ...e, measureUnit: unit } : e
                            )
                        );
                    });
                }
                
                return updatedEntry;
            }
            return entry;
        });
        setMeasureDataEntries(updatedEntries);
    };

    const validateEntries = () => {
        const errors = [];
        
        measureDataEntries.forEach((entry, index) => {
            if (!entry.measure) {
                errors.push(`Row ${index + 1}: Please select a measure`);
            }
            if (!entry.hospital) {
                errors.push(`Row ${index + 1}: Please select a hospital`);
            }
            if (!entry.value || entry.value <= 0) {
                errors.push(`Row ${index + 1}: Please enter a valid value`);
            }
        });

        return errors;
    };

    const handleBulkSubmit = async () => {
        const validationErrors = validateEntries();
        if (validationErrors.length > 0) {
            toast.error(validationErrors[0]); 
            return;
        }

        const startingDateFormatted = formatDate(startingDate);
        const endingDateFormatted = formatDate(endDate);
        
        if (!startingDateFormatted || !endingDateFormatted) {
            toast.error('Invalid date format');
            return;
        }

        setIsSaving(true);

        try {
            const measureDataArray = measureDataEntries.map(entry => ({
                measure: entry.measure,
                hospital: entry.hospital,
                value: entry.value,
                starting_date: startingDateFormatted,
                end_date: endingDateFormatted
            }));

            const promises = measureDataArray.map(data => 
                api.post(`${API_URL}/measures/new_measure_data/`, data)
            );

            const results = await Promise.allSettled(promises);
            
            const successful = results.filter(result => result.status === 'fulfilled').length;
            const failed = results.filter(result => result.status === 'rejected').length;

            if (successful > 0) {
                toast.success(`Successfully added ${successful} measure data entries`);
                if (failed > 0) {
                    toast.error(`Failed to add ${failed} entries`);
                }
                
                onClose();
                if (typeof onAddSuccess === 'function') {
                    setTimeout(() => {
                        onAddSuccess();
                    }, 100);
                }
            } else {
                toast.error('Failed to add any measure data entries');
            }
        } catch (error) {
            console.error('Error adding bulk measure data:', error);
            toast.error('Failed to add measure data. Please try again.');
        } finally {
            setIsSaving(false);
        }
    };

    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget && !isDatePickerOpen) {
            onClose();
        }
    };

    return (
        <div className="bulk-measure-overlay" onClick={handleOverlayClick}>
            <div className="bulk-measure-form" ref={popupRef}>
                <h2>Add Multiple Measure Data</h2>
                
                <div className="date-range-section">
                    {isDatePickerOpen && (
                        <div style={{
                            fontSize: '12px',
                            color: '#666',
                            marginBottom: '10px',
                            fontStyle: 'italic'
                        }}>
                            📅 Date picker is open - click outside the calendar to close it
                        </div>
                    )}
                    <div className="date-inputs">
                        <div className="date-field">
                            <label>Start Date</label>
                            <div
                                onMouseDown={(e) => e.stopPropagation()}
                                onClick={(e) => e.stopPropagation()}
                                onFocus={() => setIsDatePickerOpen(true)}
                                onBlur={() => setTimeout(() => setIsDatePickerOpen(false), 200)}
                            >
                                <DateInput
                                    date={startingDate}
                                    setDate={setStartDate}
                                    placeholder="Starting date"
                                />
                            </div>
                        </div>
                        <div className="date-field">
                            <label>End Date</label>
                            <div
                                onMouseDown={(e) => e.stopPropagation()}
                                onClick={(e) => e.stopPropagation()}
                                onFocus={() => setIsDatePickerOpen(true)}
                                onBlur={() => setTimeout(() => setIsDatePickerOpen(false), 200)}
                            >
                                <DateInput
                                    date={endDate}
                                    setDate={setEndDate}
                                    placeholder="End date"
                                />
                            </div>
                        </div>
                    </div>
                    
                    {dateError && (
                        <p className="date-error">{dateError}</p>
                    )}
                </div>

                {startingDate && endDate && isDateValid && hasYearData ? (
                    <div className="bulk-entries-section">
                        <div className="entries-header">
                            <h3>Measure Data Entries</h3>
                            <PrimaryButton
                                iconClass={<PlusSignIcon />}
                                buttonText="Add Row"
                                onClick={addNewEntry}
                            />
                        </div>
                        
                        <div className="entries-table">
                            <div className="table-header">
                                <div className="header-cell">Measure</div>
                                <div className="header-cell">Hospital</div>
                                <div className="header-cell">Value</div>
                                <div className="header-cell">Actions</div>
                            </div>
                            
                            <div className="table-body">
                                {measureDataEntries.map((entry, index) => (
                                    <div key={entry.id} className="table-row">
                                        <div className="table-cell">
                                            <select
                                                value={entry.measure}
                                                onChange={(e) => updateEntry(entry.id, 'measure', e.target.value)}
                                                className="measure-select"
                                                required
                                            >
                                                <option value="">Select Measure</option>
                                                {measures.map((measure, idx) => (
                                                    <option key={idx} value={measure}>
                                                        {measure}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                        
                                        <div className="table-cell">
                                            <select
                                                value={entry.hospital}
                                                onChange={(e) => updateEntry(entry.id, 'hospital', e.target.value)}
                                                className="hospital-select"
                                                required
                                            >
                                                <option value="">Select Hospital</option>
                                                {hospitals.map((hospital, idx) => (
                                                    <option key={idx} value={hospital}>
                                                        {hospital}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                        
                                        <div className="table-cell">
                                            <div className="value-input-container">
                                                <input
                                                    type="number"
                                                    value={entry.value}
                                                    onChange={(e) => updateEntry(entry.id, 'value', e.target.value)}
                                                    placeholder="Enter value"
                                                    className="value-input"
                                                    min="0"
                                                    step="0.01"
                                                    required
                                                />
                                                <span className="unit-symbol">
                                                    {entry.measureUnit === "percentage" ? "%" : "#"}
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div className="table-cell">
                                            <div className="action-buttons-cell">
                                                <button
                                                    type="button"
                                                    onClick={() => duplicateEntry(entry.id)}
                                                    className="duplicate-btn"
                                                    title="Duplicate this row"
                                                >
                                                    <Copy01Icon size={16} />
                                                </button>
                                                {measureDataEntries.length > 1 && (
                                                    <button
                                                        type="button"
                                                        onClick={() => removeEntry(entry.id)}
                                                        className="remove-btn"
                                                        title="Remove this row"
                                                    >
                                                        <Delete01Icon size={16} />
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                        
                        <div className="form-actions">
                            <SecondaryButton
                                onClick={onClose}
                                buttonText="Cancel"
                            />
                            <PrimaryButton
                                buttonText={`Save ${measureDataEntries.length} Entries`}
                                onClick={handleBulkSubmit}
                                isLoading={isSaving}
                                processingText="Saving..."
                            />
                        </div>
                    </div>
                ) : startingDate && endDate && isDateValid && !hasYearData ? (
                    <p className="no-data-message">No measures available for the selected year.</p>
                ) : startingDate && endDate && !isDateValid ? (
                    <p className="date-error">{dateError}</p>
                ) : (
                    <p className="instruction-message">Please select both starting and ending dates to continue.</p>
                )}
            </div>
        </div>
    );
};

export default BulkAddMeasureData;
