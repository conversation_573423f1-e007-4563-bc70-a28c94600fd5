

.popup-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 500px;
  width: 100%;

  h2 {
    color: #333;
  }
}

.copy-measures-modal {
  background-color: #fff;
  
  .modal-content {
    background-color: #fff;
  }

  .form-control {
    margin-bottom: 20px;
    background-color: #fff;
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
    }

    select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      background-color: #fff;
    }
  }

  .measures-list {
    border: 1px solid #ccc;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
    background-color: #fff;

    .select-all {
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      background-color: #fff;
    }

    .measure-item {
      display: flex;
      align-items: center;
      padding: 4px 0;
      gap: 8px;
      background-color: #fff;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: #fff;

    button {
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .cancel-button {
      background-color: transparent;
      border: 1px solid #ccc;
      color: #333;
    }

    .copy-button {
      background-color: #07AEEF;
      border: none;
      color: white;
    }
  }
}
